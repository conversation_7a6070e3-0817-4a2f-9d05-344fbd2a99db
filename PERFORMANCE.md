# Performance Optimization Guide

This document outlines the performance optimizations implemented in The Ice Box Hockey website.

## Implemented Optimizations

### 1. Code Splitting & Lazy Loading
- **Route-level splitting**: Pages are lazy-loaded using React.lazy()
- **Component-level splitting**: Non-critical sections are lazy-loaded
- **Development tools**: Stagewise tools are only loaded in development mode
- **Suspense boundaries**: Proper loading states for all lazy components

### 2. Image Optimization
- **OptimizedImage component**: Custom component with intersection observer
- **Lazy loading**: Images load only when entering viewport
- **Error handling**: Graceful fallbacks for failed image loads
- **Placeholder states**: Smooth loading experience with skeleton states
- **Resource hints**: DNS prefetch and preload for critical images

### 3. Bundle Optimization
- **Manual chunk splitting**: Vendor, UI, utils, and icons separated
- **Tree shaking**: Unused code eliminated during build
- **ESBuild minification**: Faster and more efficient minification
- **Target optimization**: Modern ES syntax for better performance

### 4. Query Optimization
- **TanStack Query configuration**: Optimized caching and retry strategies
- **Stale time**: 5-minute stale time to reduce unnecessary requests
- **Garbage collection**: 10-minute cache time for memory management
- **Reduced retries**: Single retry to prevent excessive network requests

### 5. Map Loading Optimization
- **Intersection Observer**: Maps load only when visible
- **Loading states**: Smooth transitions with loading indicators
- **Error boundaries**: Graceful handling of map loading failures
- **Reduced re-renders**: Eliminated unnecessary state updates

### 6. Development Performance
- **Fast Refresh**: Enabled for faster development iterations
- **Dependency pre-bundling**: Common dependencies pre-bundled for faster startup
- **Optimized dev server**: Configured for optimal development experience

### 7. Performance Monitoring
- **Core Web Vitals**: FCP, LCP, FID, CLS, TTFB tracking
- **Production-only**: Monitoring only runs in production builds
- **Console logging**: Performance metrics logged for debugging
- **Analytics ready**: Structure prepared for analytics integration

## Performance Metrics Targets

### Core Web Vitals
- **First Contentful Paint (FCP)**: < 1.8s
- **Largest Contentful Paint (LCP)**: < 2.5s
- **First Input Delay (FID)**: < 100ms
- **Cumulative Layout Shift (CLS)**: < 0.1
- **Time to First Byte (TTFB)**: < 600ms

### Bundle Size Targets
- **Initial bundle**: < 200KB gzipped
- **Vendor chunk**: < 150KB gzipped
- **Route chunks**: < 50KB gzipped each

## Best Practices Implemented

### 1. Component Optimization
- Memoization where appropriate
- Proper key props for lists
- Avoided unnecessary re-renders
- Optimized event handlers

### 2. Asset Loading
- WebP format for images
- Lazy loading for non-critical assets
- Resource hints for external domains
- Preloading for critical assets

### 3. Network Optimization
- DNS prefetching for external resources
- Reduced HTTP requests through bundling
- Optimized caching strategies
- Compression-ready builds

### 4. Runtime Performance
- Intersection Observer for viewport detection
- Debounced scroll events
- Efficient DOM updates
- Memory leak prevention

## Monitoring & Debugging

### Development
```bash
# Run with performance profiling
npm run dev

# Build and analyze bundle
npm run build
npm run preview
```

### Production Monitoring
- Performance metrics logged to console
- Core Web Vitals tracked automatically
- Error boundaries for graceful failures
- Loading states for all async operations

## Future Optimizations

### Potential Improvements
1. **Service Worker**: Implement for offline functionality and caching
2. **Image CDN**: Consider using a CDN for image optimization
3. **Critical CSS**: Extract and inline critical CSS
4. **Preloading**: Implement route preloading on hover
5. **Web Workers**: Move heavy computations to web workers

### Analytics Integration
```typescript
// Example analytics integration
const sendPerformanceMetrics = (metrics: PerformanceMetrics) => {
  // Send to your analytics service
  analytics.track('performance_metrics', metrics);
};
```

## Testing Performance

### Tools
- **Lighthouse**: Regular audits for performance scores
- **WebPageTest**: Detailed performance analysis
- **Chrome DevTools**: Performance profiling and debugging
- **Bundle Analyzer**: Analyze bundle composition

### Commands
```bash
# Lighthouse audit
npx lighthouse http://localhost:8080 --view

# Bundle analysis
npm run build
npx vite-bundle-analyzer dist
```

## Maintenance

### Regular Tasks
1. Monitor Core Web Vitals monthly
2. Update dependencies quarterly
3. Review bundle sizes after major changes
4. Test performance on various devices and networks
5. Optimize images as new content is added

### Performance Budget
- Monitor bundle sizes in CI/CD
- Set up alerts for performance regressions
- Regular performance audits
- User experience monitoring