{"name": "The Ice Box Hockey Equipment Store", "short_name": "Ice Box Hockey", "description": "Premier hockey equipment store in Harbor City, CA. Professional fitting, elite gear, and expert services.", "start_url": "/", "display": "standalone", "background_color": "#1b263b", "theme_color": "#1b263b", "orientation": "portrait-primary", "scope": "/", "lang": "en-US", "dir": "ltr", "categories": ["sports", "shopping", "business"], "icons": [{"src": "/favicon.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "72x72", "type": "image/webp", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "96x96", "type": "image/webp", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "128x128", "type": "image/webp", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "144x144", "type": "image/webp", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "152x152", "type": "image/webp", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "192x192", "type": "image/webp", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "384x384", "type": "image/webp", "purpose": "any"}, {"src": "/Icebox.webp", "sizes": "512x512", "type": "image/webp", "purpose": "any"}, {"src": "/favicon.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "maskable"}], "screenshots": [{"src": "/og-image.jpg", "sizes": "1200x630", "type": "image/jpeg", "platform": "wide", "label": "The Ice Box Hockey Store Homepage"}], "shortcuts": [{"name": "Team Sales", "short_name": "Team Sales", "description": "Bay Harbor Red Wings merchandise", "url": "/teamsales", "icons": [{"src": "/Icebox.webp", "sizes": "96x96", "type": "image/webp"}]}, {"name": "Store Location", "short_name": "Location", "description": "Find our store in Harbor City", "url": "/#about", "icons": [{"src": "/Icebox.webp", "sizes": "96x96", "type": "image/webp"}]}, {"name": "Services", "short_name": "Services", "description": "Professional hockey equipment services", "url": "/#services", "icons": [{"src": "/Icebox.webp", "sizes": "96x96", "type": "image/webp"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_includes": false, "display_override": ["window-controls-overlay", "standalone", "minimal-ui"], "protocol_handlers": [], "file_handlers": [], "share_target": {"action": "/", "method": "GET", "params": {"title": "title", "text": "text", "url": "url"}}}