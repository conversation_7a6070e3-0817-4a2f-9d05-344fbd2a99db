# Netlify Headers for The Ice Box Hockey
# Optimized caching and security headers

/*
  # Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  
  # Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com https://analytics.google.com; frame-src https://www.google.com;

# Static Assets - Long-term caching
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Images - Medium-term caching with revalidation
/*.webp
  Cache-Control: public, max-age=2592000, stale-while-revalidate=86400
  
/*.jpg
  Cache-Control: public, max-age=2592000, stale-while-revalidate=86400
  
/*.png
  Cache-Control: public, max-age=2592000, stale-while-revalidate=86400

# Fonts - Long-term caching
/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  
/*.woff
  Cache-Control: public, max-age=31536000, immutable

# HTML - Short-term caching with revalidation
/*.html
  Cache-Control: public, max-age=3600, stale-while-revalidate=86400
  
/
  Cache-Control: public, max-age=3600, stale-while-revalidate=86400

# API responses (future)
/api/*
  Cache-Control: public, max-age=300, stale-while-revalidate=60

# Service Worker
/sw.js
  Cache-Control: public, max-age=0, must-revalidate