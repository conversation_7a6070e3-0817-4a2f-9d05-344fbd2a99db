/**
 * Service Worker for The Ice Box Hockey
 * Provides offline support and caching strategies
 */

const CACHE_NAME = 'ice-box-v1.0.0';
const STATIC_CACHE = 'ice-box-static-v1.0.0';
const DYNAMIC_CACHE = 'ice-box-dynamic-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/teamsales',
  '/harbor-city',
  '/Icebox.webp',
  '/Whisk_cbee896f2a.webp',
  '/manifest.json',
  '/favicon.svg',
  '/favicon.ico',
  '/og-image.jpg'
];

// Assets to cache on first request
const CACHE_STRATEGIES = {
  // Cache first for static assets
  CACHE_FIRST: [
    /\.(?:js|css|woff2?|png|jpg|jpeg|webp|svg)$/,
    /\/assets\//
  ],
  
  // Network first for HTML and API
  NETWORK_FIRST: [
    /\.(?:html)$/,
    /\/api\//
  ],
  
  // Stale while revalidate for images
  STALE_WHILE_REVALIDATE: [
    /\/.*\.(?:png|jpg|jpeg|webp|gif|svg)$/
  ]
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Failed to cache static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip external requests (except Google Analytics)
  if (url.origin !== location.origin && !url.hostname.includes('google')) {
    return;
  }

  event.respondWith(handleRequest(request));
});

async function handleRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  try {
    // Determine caching strategy
    if (matchesPattern(pathname, CACHE_STRATEGIES.CACHE_FIRST)) {
      return await cacheFirst(request);
    } else if (matchesPattern(pathname, CACHE_STRATEGIES.NETWORK_FIRST)) {
      return await networkFirst(request);
    } else if (matchesPattern(pathname, CACHE_STRATEGIES.STALE_WHILE_REVALIDATE)) {
      return await staleWhileRevalidate(request);
    } else {
      // Default to network first
      return await networkFirst(request);
    }
  } catch (error) {
    console.error('Service Worker: Request failed', error);
    return await getOfflineFallback(request);
  }
}

// Cache first strategy
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  const networkResponse = await fetch(request);
  if (networkResponse.ok) {
    const cache = await caches.open(DYNAMIC_CACHE);
    cache.put(request, networkResponse.clone());
  }
  
  return networkResponse;
}

// Network first strategy
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Stale while revalidate strategy
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request);
  
  const networkResponsePromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      const cache = caches.open(DYNAMIC_CACHE);
      cache.then(c => c.put(request, networkResponse.clone()));
    }
    return networkResponse;
  });
  
  return cachedResponse || networkResponsePromise;
}

// Get offline fallback
async function getOfflineFallback(request) {
  const url = new URL(request.url);
  
  // Return cached page for navigation requests
  if (request.mode === 'navigate') {
    const cachedPage = await caches.match('/');
    if (cachedPage) {
      return cachedPage;
    }
  }
  
  // Return placeholder for images
  if (request.destination === 'image') {
    return new Response(
      '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#f3f4f6"/><text x="100" y="100" text-anchor="middle" dy=".3em" fill="#9ca3af">Offline</text></svg>',
      { headers: { 'Content-Type': 'image/svg+xml' } }
    );
  }
  
  throw new Error('No offline fallback available');
}

// Helper function to match URL patterns
function matchesPattern(pathname, patterns) {
  return patterns.some(pattern => pattern.test(pathname));
}

// Background sync for analytics (future enhancement)
self.addEventListener('sync', (event) => {
  if (event.tag === 'analytics-sync') {
    event.waitUntil(syncAnalytics());
  }
});

async function syncAnalytics() {
  // Sync offline analytics data when connection is restored
  console.log('Service Worker: Syncing analytics data');
}