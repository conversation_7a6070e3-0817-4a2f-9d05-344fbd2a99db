<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ice Box Hockey</title>
    <meta name="description" content="Ice Box Hockey - Your premier destination for hockey equipment, gear, and accessories. Find top brands, expert advice, and everything you need for the game." />
    <meta name="author" content="Ice Box Hockey Team" />
    <meta name="keywords" content="hockey equipment, ice hockey gear, hockey accessories, hockey shop, hockey sticks, skates, protective gear" />

    <meta property="og:title" content="Ice Box Hockey - Premium Hockey Equipment & Gear" />
    <meta property="og:description" content="Discover premium hockey equipment, gear, and accessories at Ice Box Hockey. Top brands, expert advice, and everything you need for the game." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og-image.jpg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@puckdrop" />
    <meta name="twitter:image" content="/og-image.jpg" />
    
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="alternate icon" href="/favicon.ico" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#1b263b" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Ice Box Hockey" />
    <link rel="apple-touch-icon" href="/Icebox.webp" />

    <!-- Resource Hints for Performance -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//fonts.gstatic.com" />
    <link rel="dns-prefetch" href="//www.google.com" />
    <link rel="dns-prefetch" href="//maps.googleapis.com" />
    
    <!-- Preload critical assets -->
    <link rel="preload" href="/Icebox.webp" as="image" type="image/webp" />
    <link rel="preload" href="/Whisk_cbee896f2a.webp" as="image" type="image/webp" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Google Maps API will be loaded by loadGoogleMaps.ts -->

    <!-- Google Analytics 4 - The Ice Box Hockey -->
    <!-- Stream: iceboxhockey.com (ID: 11376792355) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-VL34HQ8M9H"></script>
    <script>
      // Initialize Google Analytics
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      // Configure with enhanced settings for The Ice Box
      gtag('config', 'G-VL34HQ8M9H', {
        // Privacy-focused configuration
        allow_google_signals: true,
        allow_ad_personalization_signals: false,
        
        // Enhanced measurement
        enhanced_measurement: {
          scrolls: true,
          outbound_clicks: true,
          site_search: false,
          video_engagement: false,
          file_downloads: true
        },
        
        // Custom configuration for hockey store
        custom_map: {
          'custom_parameter_1': 'store_section',
          'custom_parameter_2': 'hockey_interest'
        },
        
        // Performance and debugging
        send_page_view: true,
        debug_mode: false // Will be overridden by analytics.ts in development
      });

      // Track initial page load
      gtag('event', 'page_view', {
        page_title: 'The Ice Box - Hockey Equipment Store',
        page_location: window.location.href,
        content_group1: 'hockey_equipment',
        content_group2: 'store_website'
      });
    </script>
  </head>

  <body class="min-h-screen bg-gray-50">
    <div id="root">
      <nav>
        <ul>
          <li><a href="index.html">Home</a></li>
          <li><a href="about.html">About</a></li>
          <li><a href="/teamsales">Team Sales</a></li>
          <li><a href="contact.html">Contact</a></li>
        </ul>
      </nav>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
