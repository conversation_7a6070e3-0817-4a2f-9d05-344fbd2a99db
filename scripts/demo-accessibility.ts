#!/usr/bin/env tsx

/**
 * Demo script for accessibility validation system
 * Tests the accessibility validation functionality with the Ice Box Hockey website
 */

import { AccessibilityValidator, AccessibilityTestSuite } from '../src/verification/accessibility.js';
import { ConfigManager } from '../src/verification/config.js';

async function runAccessibilityDemo() {
  console.log('🔍 Ice Box Hockey - Accessibility Validation Demo');
  console.log('================================================\n');

  try {
    // Test 1: Basic AccessibilityValidator
    console.log('📋 Test 1: Basic Accessibility Validation');
    console.log('------------------------------------------');
    
    const validator = new AccessibilityValidator({
      baseUrl: 'http://localhost:4175',
      pages: ['/', '/team-sales'],
      wcagLevel: 'AA',
      timeout: 30000,
      keyboardNavigation: true,
      colorContrast: true,
      screenReader: true,
    });

    console.log('⏳ Running accessibility validation...');
    const startTime = Date.now();
    
    const result = await validator.validate();
    const duration = Date.now() - startTime;

    console.log(`✅ Validation completed in ${duration}ms`);
    console.log(`📊 Results:`);
    console.log(`   - Compliant: ${result.compliant ? '✅ Yes' : '❌ No'}`);
    console.log(`   - Pages tested: ${result.testedPages.length}`);
    console.log(`   - Violations: ${result.violations.length}`);
    console.log(`   - Warnings: ${result.warnings.length}`);

    if (result.violations.length > 0) {
      console.log('\n🚨 Violations found:');
      result.violations.slice(0, 3).forEach((violation, index) => {
        console.log(`   ${index + 1}. ${violation.rule} (${violation.impact})`);
        console.log(`      Element: ${violation.element}`);
        console.log(`      Issue: ${violation.description.substring(0, 100)}...`);
      });
      
      if (result.violations.length > 3) {
        console.log(`   ... and ${result.violations.length - 3} more violations`);
      }
    }

    if (result.warnings.length > 0) {
      console.log('\n⚠️  Warnings found:');
      result.warnings.slice(0, 2).forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning.rule}`);
        console.log(`      Element: ${warning.element}`);
        console.log(`      Issue: ${warning.description.substring(0, 100)}...`);
      });
    }

    // Test 2: Generate accessibility report
    console.log('\n📄 Test 2: Accessibility Report Generation');
    console.log('-------------------------------------------');
    
    const report = validator.generateReport(result);
    console.log('✅ Report generated successfully');
    console.log(`📝 Report length: ${report.length} characters`);
    
    // Show first few lines of the report
    const reportLines = report.split('\n');
    console.log('\n📋 Report preview:');
    reportLines.slice(0, 8).forEach(line => {
      console.log(`   ${line}`);
    });
    
    if (reportLines.length > 8) {
      console.log(`   ... and ${reportLines.length - 8} more lines`);
    }

    // Test 3: AccessibilityTestSuite integration
    console.log('\n🧪 Test 3: Test Suite Integration');
    console.log('----------------------------------');
    
    const configManager = new ConfigManager({
      accessibilityLevel: 'AA',
      testSuites: [{
        name: 'accessibility-tests',
        type: 'accessibility',
        enabled: true,
        timeout: 60000,
        retries: 2,
      }],
    });

    const testSuite = new AccessibilityTestSuite(configManager);
    
    console.log('⏳ Running accessibility test suite...');
    const suiteStartTime = Date.now();
    
    const testResult = await testSuite.execute();
    const suiteDuration = Date.now() - suiteStartTime;

    console.log(`✅ Test suite completed in ${suiteDuration}ms`);
    console.log(`📊 Test Results:`);
    console.log(`   - Passed: ${testResult.passed ? '✅ Yes' : '❌ No'}`);
    console.log(`   - Duration: ${testResult.duration}ms`);
    console.log(`   - Test count: ${testResult.testCount}`);
    console.log(`   - Failures: ${testResult.failures.length}`);

    if (testResult.failures.length > 0) {
      console.log('\n❌ Test failures:');
      testResult.failures.slice(0, 3).forEach((failure, index) => {
        console.log(`   ${index + 1}. ${failure.testName}`);
        console.log(`      Error: ${failure.error.substring(0, 100)}...`);
      });
    }

    // Test 4: Configuration validation
    console.log('\n⚙️  Test 4: Configuration Validation');
    console.log('------------------------------------');
    
    const configValidation = configManager.validateConfig();
    console.log(`✅ Configuration validation: ${configValidation.valid ? 'Valid' : 'Invalid'}`);
    
    if (!configValidation.valid) {
      console.log('❌ Configuration errors:');
      configValidation.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    // Test 5: Different WCAG levels
    console.log('\n🎯 Test 5: WCAG Level Testing');
    console.log('------------------------------');
    
    const wcagLevels: Array<'A' | 'AA' | 'AAA'> = ['A', 'AA', 'AAA'];
    
    for (const level of wcagLevels) {
      console.log(`\n   Testing WCAG ${level} level:`);
      
      const levelValidator = new AccessibilityValidator({
        baseUrl: 'http://localhost:4175',
        pages: ['/'],
        wcagLevel: level,
        timeout: 15000,
      });

      try {
        const levelResult = await levelValidator.validate();
        console.log(`   ✅ WCAG ${level}: ${levelResult.compliant ? 'Compliant' : 'Non-compliant'}`);
        console.log(`      Violations: ${levelResult.violations.length}, Warnings: ${levelResult.warnings.length}`);
      } catch (error) {
        console.log(`   ❌ WCAG ${level}: Test failed - ${error}`);
      }
    }

    console.log('\n🎉 Accessibility validation demo completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Basic validation working');
    console.log('   ✅ Report generation working');
    console.log('   ✅ Test suite integration working');
    console.log('   ✅ Configuration validation working');
    console.log('   ✅ WCAG level testing working');

  } catch (error) {
    console.error('\n❌ Demo failed:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
    }
    
    console.log('\n💡 Troubleshooting tips:');
    console.log('   1. Make sure the development server is running on http://localhost:4175');
    console.log('   2. Ensure Playwright browsers are installed: npx playwright install');
    console.log('   3. Check that axe-core dependencies are properly installed');
    console.log('   4. Verify network connectivity to the test server');
    
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Demo interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n⏹️  Demo terminated');
  process.exit(0);
});

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  runAccessibilityDemo().catch(error => {
    console.error('Unhandled error in accessibility demo:', error);
    process.exit(1);
  });
}

export { runAccessibilityDemo };