#!/usr/bin/env node

/**
 * Main verification script for production deployment readiness
 * This script orchestrates all verification checks and provides deployment decision
 */

import { runVerification } from '../src/verification/pipeline';
import { createConfigManager } from '../src/verification/config';
import { VerificationReport, PipelineOptions } from '../src/verification/types';

interface CLIOptions {
  config?: string;
  skip?: string[];
  verbose?: boolean;
  output?: 'json' | 'html' | 'console';
  help?: boolean;
}

/**
 * Parse command line arguments
 */
function parseArgs(): CLIOptions {
  const args = process.argv.slice(2);
  const options: CLIOptions = {
    verbose: false,
    output: 'console',
    skip: [],
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--config':
      case '-c':
        options.config = args[++i];
        break;
      case '--skip':
      case '-s':
        options.skip = args[++i]?.split(',') || [];
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--output':
      case '-o':
        const outputFormat = args[++i] as 'json' | 'html' | 'console';
        if (['json', 'html', 'console'].includes(outputFormat)) {
          options.output = outputFormat;
        }
        break;
      case '--help':
      case '-h':
        options.help = true;
        break;
    }
  }

  return options;
}

/**
 * Display help information
 */
function showHelp(): void {
  console.log(`
Production Deployment Verification Tool

Usage: npm run verify [options]

Options:
  -c, --config <path>     Path to custom configuration file
  -s, --skip <stages>     Comma-separated list of stages to skip
  -v, --verbose           Enable verbose logging
  -o, --output <format>   Output format: json, html, console (default: console)
  -h, --help              Show this help message

Examples:
  npm run verify                           # Run all verification stages
  npm run verify --verbose                 # Run with detailed logging
  npm run verify --skip build,tests       # Skip build and test stages
  npm run verify --output json            # Output results as JSON
  npm run verify --config ./custom.json   # Use custom configuration

Stages:
  - build         Verify application builds successfully
  - tests         Run unit, integration, and E2E tests
  - performance   Check Core Web Vitals and Lighthouse scores
  - accessibility Validate WCAG 2.1 AA compliance
  - pwa          Verify PWA functionality
  - dependencies  Check external service availability
`);
}

/**
 * Format and display verification report
 */
function displayReport(report: VerificationReport, format: 'json' | 'html' | 'console'): void {
  switch (format) {
    case 'json':
      console.log(JSON.stringify(report, null, 2));
      break;
    
    case 'html':
      generateHTMLReport(report);
      break;
    
    case 'console':
    default:
      displayConsoleReport(report);
      break;
  }
}

/**
 * Display report in console format
 */
function displayConsoleReport(report: VerificationReport): void {
  console.log('\n' + '='.repeat(60));
  console.log('🚀 PRODUCTION DEPLOYMENT VERIFICATION REPORT');
  console.log('='.repeat(60));
  
  console.log(`\n📅 Timestamp: ${report.timestamp.toISOString()}`);
  console.log(`📊 Overall Status: ${getStatusEmoji(report.overallStatus)} ${report.overallStatus.toUpperCase()}`);
  console.log(`🚢 Deployment Ready: ${report.deploymentReady ? '✅ YES' : '❌ NO'}`);

  // Build Verification
  console.log('\n🔨 BUILD VERIFICATION');
  console.log('-'.repeat(30));
  const build = report.buildVerification;
  console.log(`Status: ${build.success ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`Build Time: ${build.buildTime}ms`);
  console.log(`Output Size: ${(build.outputSize.total / 1024 / 1024).toFixed(2)} MB`);
  if (build.errors.length > 0) {
    console.log(`Errors: ${build.errors.length}`);
    build.errors.forEach(error => console.log(`  - ${error.message}`));
  }
  if (build.warnings.length > 0) {
    console.log(`Warnings: ${build.warnings.length}`);
  }

  // Test Results
  console.log('\n🧪 TEST RESULTS');
  console.log('-'.repeat(30));
  if (report.testResults.length === 0) {
    console.log('No test results available');
  } else {
    report.testResults.forEach(test => {
      console.log(`${test.passed ? '✅' : '❌'} Tests: ${test.testCount} (${test.duration}ms)`);
      if (test.failures.length > 0) {
        console.log(`  Failures: ${test.failures.length}`);
      }
    });
  }

  // Performance Metrics
  console.log('\n⚡ PERFORMANCE METRICS');
  console.log('-'.repeat(30));
  const perf = report.performanceMetrics;
  console.log(`LCP: ${perf.lcp}ms ${perf.lcp <= 2500 ? '✅' : '❌'}`);
  console.log(`FID: ${perf.fid}ms ${perf.fid <= 100 ? '✅' : '❌'}`);
  console.log(`CLS: ${perf.cls} ${perf.cls <= 0.1 ? '✅' : '❌'}`);
  console.log(`Lighthouse Performance: ${perf.lighthouse.performance}/100 ${perf.lighthouse.performance >= 90 ? '✅' : '❌'}`);

  // Accessibility
  console.log('\n♿ ACCESSIBILITY');
  console.log('-'.repeat(30));
  const a11y = report.accessibilityResults;
  console.log(`WCAG Compliant: ${a11y.compliant ? '✅ YES' : '❌ NO'}`);
  console.log(`Violations: ${a11y.violations.length}`);
  console.log(`Warnings: ${a11y.warnings.length}`);

  // PWA Status
  console.log('\n📱 PWA STATUS');
  console.log('-'.repeat(30));
  const pwa = report.pwaValidation;
  console.log(`Service Worker: ${pwa.serviceWorkerRegistered ? '✅' : '❌'}`);
  console.log(`Manifest Valid: ${pwa.manifestValid ? '✅' : '❌'}`);
  console.log(`Offline Ready: ${pwa.offlineFunctionality ? '✅' : '❌'}`);
  console.log(`Installable: ${pwa.installable ? '✅' : '❌'}`);

  // External Dependencies
  console.log('\n🌐 EXTERNAL DEPENDENCIES');
  console.log('-'.repeat(30));
  const deps = report.dependencyStatus;
  console.log(`Google Maps: ${deps.googleMaps.available ? '✅' : '❌'} (${deps.googleMaps.responseTime}ms)`);
  if (deps.cdnResources.length > 0) {
    deps.cdnResources.forEach(cdn => {
      console.log(`${cdn.service}: ${cdn.available ? '✅' : '❌'} (${cdn.responseTime}ms)`);
    });
  }

  // Recommendations
  if (report.recommendations.length > 0) {
    console.log('\n💡 RECOMMENDATIONS');
    console.log('-'.repeat(30));
    report.recommendations.forEach(rec => console.log(`• ${rec}`));
  }

  console.log('\n' + '='.repeat(60));
}

/**
 * Generate HTML report (placeholder for future implementation)
 */
function generateHTMLReport(report: VerificationReport): void {
  console.log('HTML report generation will be implemented in a future task');
  console.log('For now, displaying JSON output:');
  console.log(JSON.stringify(report, null, 2));
}

/**
 * Get status emoji
 */
function getStatusEmoji(status: string): string {
  switch (status) {
    case 'passed': return '✅';
    case 'warning': return '⚠️';
    case 'failed': return '❌';
    default: return '❓';
  }
}

/**
 * Main execution function
 */
async function main(): Promise<void> {
  try {
    const options = parseArgs();

    if (options.help) {
      showHelp();
      process.exit(0);
    }

    console.log('🚀 Starting production deployment verification...\n');

    // Load configuration
    let configManager;
    if (options.config) {
      console.log(`Loading configuration from: ${options.config}`);
      configManager = await import('../src/verification/config').then(m => 
        m.ConfigManager.loadFromFile(options.config!)
      );
    } else {
      configManager = createConfigManager();
    }

    // Create pipeline options
    const pipelineOptions: PipelineOptions = {
      config: configManager.getConfig(),
      skipStages: options.skip,
      verbose: options.verbose,
      outputFormat: options.output,
    };

    // Execute verification pipeline
    const report = await runVerification(pipelineOptions);

    // Display results
    displayReport(report, options.output || 'console');

    // Exit with appropriate code
    const exitCode = report.deploymentReady ? 0 : 1;
    console.log(`\n🏁 Verification completed with exit code: ${exitCode}`);
    process.exit(exitCode);

  } catch (error) {
    console.error('❌ Verification failed with error:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { main as runVerificationScript };