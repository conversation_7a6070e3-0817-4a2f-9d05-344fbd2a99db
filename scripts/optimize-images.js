#!/usr/bin/env node

/**
 * Image Optimization Script for The Ice Box
 * Converts images to WebP format and generates responsive sizes
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const PUBLIC_DIR = path.join(__dirname, '../public');
const SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.png'];
const RESPONSIVE_SIZES = [400, 800, 1200, 1600];

async function optimizeImage(inputPath, outputDir) {
  const filename = path.basename(inputPath, path.extname(inputPath));
  const ext = path.extname(inputPath).toLowerCase();
  
  if (!SUPPORTED_FORMATS.includes(ext)) {
    return;
  }

  try {
    const image = sharp(inputPath);
    const metadata = await image.metadata();
    
    console.log(`Optimizing: ${filename}${ext} (${metadata.width}x${metadata.height})`);

    // Generate WebP version
    await image
      .webp({ quality: 85, effort: 6 })
      .toFile(path.join(outputDir, `${filename}.webp`));

    // Generate responsive sizes for large images
    if (metadata.width > 800) {
      for (const size of RESPONSIVE_SIZES) {
        if (size < metadata.width) {
          await image
            .resize(size, null, { withoutEnlargement: true })
            .webp({ quality: 85, effort: 6 })
            .toFile(path.join(outputDir, `${filename}-${size}w.webp`));
        }
      }
    }

    // Generate optimized original format
    if (ext === '.jpg' || ext === '.jpeg') {
      await image
        .jpeg({ quality: 85, progressive: true })
        .toFile(path.join(outputDir, `${filename}-optimized${ext}`));
    } else if (ext === '.png') {
      await image
        .png({ quality: 85, compressionLevel: 9 })
        .toFile(path.join(outputDir, `${filename}-optimized${ext}`));
    }

    console.log(`✓ Optimized: ${filename}`);
  } catch (error) {
    console.error(`✗ Error optimizing ${filename}:`, error.message);
  }
}

async function optimizeAllImages() {
  console.log('🖼️  Starting image optimization...');
  
  const files = fs.readdirSync(PUBLIC_DIR);
  const imageFiles = files.filter(file => 
    SUPPORTED_FORMATS.includes(path.extname(file).toLowerCase())
  );

  console.log(`Found ${imageFiles.length} images to optimize`);

  for (const file of imageFiles) {
    const inputPath = path.join(PUBLIC_DIR, file);
    await optimizeImage(inputPath, PUBLIC_DIR);
  }

  console.log('✅ Image optimization complete!');
  console.log('\n📊 Optimization Summary:');
  console.log('- WebP versions created for all images');
  console.log('- Responsive sizes generated for large images');
  console.log('- Original formats optimized');
  console.log('\n💡 Next steps:');
  console.log('1. Update image references to use WebP format');
  console.log('2. Implement responsive image loading');
  console.log('3. Remove original unoptimized images');
}

// Run if called directly
if (require.main === module) {
  optimizeAllImages().catch(console.error);
}

module.exports = { optimizeImage, optimizeAllImages };