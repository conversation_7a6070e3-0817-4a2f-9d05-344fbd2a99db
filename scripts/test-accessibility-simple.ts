#!/usr/bin/env tsx

/**
 * Comprehensive test script for accessibility validation system
 * Demonstrates all implemented features including axe-core integration,
 * keyboard navigation testing, color contrast validation, and screen reader compatibility
 */

import { AccessibilityValidator, AccessibilityTestSuite } from '../src/verification/accessibility.js';
import { ConfigManager } from '../src/verification/config.js';

async function testAccessibilitySystem() {
  console.log('🔍 Comprehensive Accessibility Validation System Test');
  console.log('====================================================\n');

  try {
    // Test 1: Basic axe-core integration
    console.log('📋 Test 1: Axe-core WCAG 2.1 AA Integration');
    console.log('--------------------------------------------');
    
    const basicValidator = new AccessibilityValidator({
      baseUrl: 'http://localhost:4175',
      pages: ['/'],
      wcagLevel: 'AA',
      timeout: 15000,
      keyboardNavigation: false,
      colorContrast: false,
      screenReader: false,
    });

    console.log('⏳ Running axe-core validation...');
    const basicResult = await basicValidator.validate();

    console.log('✅ Axe-core validation completed!');
    console.log(`📊 Results:`);
    console.log(`   - Compliant: ${basicResult.compliant ? '✅ Yes' : '❌ No'}`);
    console.log(`   - Violations: ${basicResult.violations.length}`);
    console.log(`   - Warnings: ${basicResult.warnings.length}`);

    // Test 2: Comprehensive validation with all features
    console.log('\n📋 Test 2: Comprehensive Validation (All Features)');
    console.log('--------------------------------------------------');
    
    const comprehensiveValidator = new AccessibilityValidator({
      baseUrl: 'http://localhost:4175',
      pages: ['/'],
      wcagLevel: 'AA',
      timeout: 20000,
      keyboardNavigation: true,
      colorContrast: true,
      screenReader: true,
    });

    console.log('⏳ Running comprehensive validation...');
    const comprehensiveResult = await comprehensiveValidator.validate();

    console.log('✅ Comprehensive validation completed!');
    console.log(`📊 Results:`);
    console.log(`   - Compliant: ${comprehensiveResult.compliant ? '✅ Yes' : '❌ No'}`);
    console.log(`   - Violations: ${comprehensiveResult.violations.length}`);
    console.log(`   - Warnings: ${comprehensiveResult.warnings.length}`);

    // Show violations with remediation guidance
    if (comprehensiveResult.violations.length > 0) {
      console.log('\n🚨 Violations with Remediation Guidance:');
      comprehensiveResult.violations.slice(0, 3).forEach((violation, index) => {
        console.log(`   ${index + 1}. ${violation.rule} (${violation.impact})`);
        console.log(`      Element: ${violation.element}`);
        console.log(`      Issue: ${violation.description.substring(0, 80)}...`);
      });
    }

    // Test 3: Report generation with remediation guidance
    console.log('\n📄 Test 3: Report Generation with Remediation Guidance');
    console.log('------------------------------------------------------');
    
    const report = comprehensiveValidator.generateReport(comprehensiveResult);
    console.log('✅ Report generated successfully');
    console.log(`📝 Report length: ${report.length} characters`);
    
    const reportLines = report.split('\n');
    console.log('\n📋 Report preview (first 10 lines):');
    reportLines.slice(0, 10).forEach(line => {
      console.log(`   ${line}`);
    });

    // Test 4: Test Suite Integration
    console.log('\n🧪 Test 4: Test Suite Integration');
    console.log('----------------------------------');
    
    const configManager = new ConfigManager({
      accessibilityLevel: 'AA',
      testSuites: [{
        name: 'accessibility-tests',
        type: 'accessibility',
        enabled: true,
        timeout: 30000,
        retries: 2,
      }],
    });

    const testSuite = new AccessibilityTestSuite(configManager);
    
    console.log('⏳ Running accessibility test suite...');
    const testResult = await testSuite.execute();

    console.log('✅ Test suite completed!');
    console.log(`📊 Test Results:`);
    console.log(`   - Passed: ${testResult.passed ? '✅ Yes' : '❌ No'}`);
    console.log(`   - Duration: ${testResult.duration}ms`);
    console.log(`   - Test count: ${testResult.testCount}`);
    console.log(`   - Failures: ${testResult.failures.length}`);

    // Test 5: Different WCAG levels
    console.log('\n🎯 Test 5: WCAG Level Compliance Testing');
    console.log('----------------------------------------');
    
    const wcagLevels: Array<'A' | 'AA' | 'AAA'> = ['A', 'AA', 'AAA'];
    
    for (const level of wcagLevels) {
      console.log(`\n   Testing WCAG ${level} compliance:`);
      
      const levelValidator = new AccessibilityValidator({
        baseUrl: 'http://localhost:4175',
        pages: ['/'],
        wcagLevel: level,
        timeout: 10000,
        keyboardNavigation: false,
        colorContrast: false,
        screenReader: false,
      });

      try {
        const levelResult = await levelValidator.validate();
        console.log(`   ✅ WCAG ${level}: ${levelResult.compliant ? 'Compliant' : 'Non-compliant'}`);
        console.log(`      Violations: ${levelResult.violations.length}, Warnings: ${levelResult.warnings.length}`);
      } catch (error) {
        console.log(`   ❌ WCAG ${level}: Test failed - ${error}`);
      }
    }

    console.log('\n🎉 All accessibility validation tests completed successfully!');
    console.log('\n📋 Feature Summary:');
    console.log('   ✅ Axe-core WCAG 2.1 AA compliance checking');
    console.log('   ✅ Keyboard navigation testing');
    console.log('   ✅ Color contrast validation');
    console.log('   ✅ Screen reader compatibility testing');
    console.log('   ✅ Comprehensive remediation guidance');
    console.log('   ✅ Test suite integration');
    console.log('   ✅ Multiple WCAG level support');
    console.log('   ✅ Error handling and graceful failures');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    
    // Check server connectivity
    try {
      const response = await fetch('http://localhost:4175');
      if (response.ok) {
        console.log('✅ Server is running');
      } else {
        console.log('❌ Server returned error:', response.status);
      }
    } catch (fetchError) {
      console.log('❌ Cannot connect to server');
      console.log('💡 Make sure to run: npm run build && npm run preview');
    }
    
    process.exit(1);
  }
}

testAccessibilitySystem();