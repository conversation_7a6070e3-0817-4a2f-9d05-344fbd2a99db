#!/usr/bin/env tsx

/**
 * Simple script to test the performance monitoring system
 */

import { chromium } from '@playwright/test';
import { createPerformanceMonitor } from '../src/verification/performance';
import { createConfigManager } from '../src/verification/config';

async function testPerformanceMonitoring() {
  console.log('🚀 Testing Performance Monitoring System...\n');

  try {
    // Create configuration
    const configManager = createConfigManager();
    const performanceMonitor = createPerformanceMonitor(configManager.getConfig());

    // Launch browser
    console.log('📱 Launching browser...');
    const browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();

    try {
      // Test with a simple HTML page
      const testHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Performance Test Page</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .content { max-width: 800px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="content">
            <h1>Performance Test Page</h1>
            <p>This is a simple test page for performance monitoring.</p>
            <div id="dynamic-content"></div>
          </div>
          <script>
            // Simulate some dynamic content loading
            setTimeout(() => {
              document.getElementById('dynamic-content').innerHTML = 
                '<p>Dynamic content loaded after 100ms</p>';
            }, 100);
          </script>
        </body>
        </html>
      `;

      // Create a data URL for the test page
      const dataUrl = `data:text/html;charset=utf-8,${encodeURIComponent(testHtml)}`;

      console.log('🔍 Running performance test...');
      const result = await performanceMonitor.runPerformanceTest(page, dataUrl);

      console.log('\n📊 Performance Test Results:');
      console.log('================================');
      console.log(`✅ Success: ${result.success}`);
      console.log(`🕐 Duration: ${result.duration}ms`);
      console.log(`🌐 URL: ${result.url.substring(0, 50)}...`);
      
      console.log('\n📈 Core Web Vitals:');
      console.log(`  LCP: ${result.metrics.lcp}ms`);
      console.log(`  FID: ${result.metrics.fid}ms`);
      console.log(`  CLS: ${result.metrics.cls}`);
      console.log(`  FCP: ${result.metrics.fcp}ms`);

      console.log('\n🏆 Lighthouse Scores:');
      console.log(`  Performance: ${result.metrics.lighthouse.performance}/100`);
      console.log(`  Accessibility: ${result.metrics.lighthouse.accessibility}/100`);
      console.log(`  Best Practices: ${result.metrics.lighthouse.bestPractices}/100`);
      console.log(`  SEO: ${result.metrics.lighthouse.seo}/100`);

      if (result.violations.length > 0) {
        console.log('\n⚠️  Performance Violations:');
        result.violations.forEach(violation => {
          const icon = violation.severity === 'error' ? '❌' : '⚠️';
          console.log(`  ${icon} ${violation.metric}: ${violation.actual} (threshold: ${violation.threshold})`);
        });
      } else {
        console.log('\n✅ No performance violations detected!');
      }

      // Test threshold validation with strict thresholds
      console.log('\n🔬 Testing threshold validation...');
      const strictConfig = createConfigManager({
        performanceThresholds: {
          lcp: 100,  // Very strict
          fid: 10,   // Very strict
          cls: 0.01, // Very strict
          lighthousePerformance: 99, // Very strict
        },
      }).getConfig();

      const strictMonitor = createPerformanceMonitor(strictConfig);
      const strictResult = await strictMonitor.runPerformanceTest(page, dataUrl);

      console.log(`Strict test violations: ${strictResult.violations.length}`);
      if (strictResult.violations.length > 0) {
        console.log('✅ Threshold validation working correctly (violations detected with strict thresholds)');
      }

    } finally {
      await page.close();
      await browser.close();
    }

    console.log('\n🎉 Performance monitoring system test completed successfully!');

  } catch (error) {
    console.error('❌ Performance monitoring test failed:', error);
    process.exit(1);
  }
}

// Run the test
testPerformanceMonitoring().catch(console.error);