# The Ice Box - Hockey Equipment Store

A high-performance, modern web application for The Ice Box hockey equipment store, built with cutting-edge technologies and optimized for exceptional user experience.

![The Ice Box Logo](/public/Icebox.webp)

[![Performance](https://img.shields.io/badge/Performance-Optimized-green.svg)](./PERFORMANCE.md)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5+-blue.svg)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18.3+-blue.svg)](https://reactjs.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4+-purple.svg)](https://vitejs.dev/)

## About The Ice Box

The Ice Box is a premier hockey equipment store located inside the Skating Edge Arena in Harbor City, California. We provide professional-grade hockey equipment, expert fitting services, and personalized recommendations from players who live and breathe the game. Our store is scheduled to open in August 2025, just in time for the 2025-2026 hockey season.

## Website Architecture

```
The Ice Box Website
│
├── Home Page (/)
│   ├── Hero Section - Store branding with animated marquee
│   ├── About Section - Store info with interactive Google Maps
│   ├── Services Section - Professional equipment & skate services
│   ├── Products Section - Equipment categories & specialized services
│   ├── Brands Section - Partner brands with interactive logos
│   └── Footer - Contact info, hours, and social links
│
├── Team Sales (/teamsales)
│   └── Bay Harbor Red Wings merchandise catalog
│
└── 404 Not Found Page - Custom error handling
```

### Performance Features
- 🚀 **Code Splitting**: Route and component-level lazy loading
- 🖼️ **Image Optimization**: Intersection Observer-based lazy loading
- 📦 **Bundle Optimization**: Manual chunk splitting for optimal caching
- 🗺️ **Smart Map Loading**: Maps load only when visible
- 📊 **Performance Monitoring**: Core Web Vitals tracking
- ⚡ **Fast Development**: Hot reload with React Fast Refresh

## Store Information

- **Location:** 23770 S Western Ave, Harbor City, CA 90710 (Inside Skating Edge Arena)
- **Opening Date:** August 2025
- **Store Hours:**
  - Monday - Friday: 10:00 AM - 8:00 PM
  - Saturday: 10:00 AM - 8:00 PM
  - Sunday: 12:00 PM - 6:00 PM

## Key Features

### 🏒 Hockey-Specific Features
- **Elite Blade Technology**: E-S4 Sharpener and E-P3 Profiler showcases
- **Professional Services**: Equipment fitting, team packages, performance consultation
- **Brand Partnerships**: 10+ major hockey brands (Bauer, CCM, Warrior, etc.)
- **Team Sales Integration**: Bay Harbor Red Wings merchandise catalog
- **Expert Recommendations**: Personalized equipment guidance

### 🚀 Technical Excellence
- **Performance Optimized**: < 2.5s LCP, code splitting, lazy loading
- **Modern Stack**: React 18, TypeScript, Vite, TailwindCSS
- **Responsive Design**: Mobile-first approach, optimized for all devices
- **Interactive Maps**: Google Maps integration with smart loading
- **Accessibility**: WCAG compliant navigation and components
- **SEO Optimized**: Meta tags, structured data, performance metrics

### 💼 Business Features
- **Store Information**: Hours, location, contact details
- **Social Integration**: Facebook and Instagram links
- **Coming Soon Banner**: Animated marquee for August 2025 opening
- **Professional Branding**: Consistent Ice Box visual identity

## Technical Specifications

### System Requirements

- **Node.js**: v18.0.0 or later
- **Package Manager**: npm v9+ or pnpm v8+ (recommended)
- **Browser Support**: Modern browsers (Chrome 90+, Firefox 88+, Safari 14+)

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    The Ice Box Architecture                 │
├─────────────────────────────────────────────────────────────┤
│  Frontend (React 18 + TypeScript)                          │
│  ├── Routing (React Router DOM)                            │
│  ├── State Management (TanStack Query + React Hooks)       │
│  ├── UI Components (shadcn/ui + Radix UI)                  │
│  └── Styling (TailwindCSS + CSS-in-JS)                     │
├─────────────────────────────────────────────────────────────┤
│  Build & Development (Vite)                                │
│  ├── Code Splitting (Route & Component Level)              │
│  ├── Bundle Optimization (Manual Chunks)                   │
│  ├── Asset Optimization (Image Lazy Loading)               │
│  └── Performance Monitoring (Core Web Vitals)              │
├─────────────────────────────────────────────────────────────┤
│  External Integrations                                      │
│  ├── Google Maps API (Interactive Store Location)          │
│  ├── Social Media (Facebook, Instagram)                    │
│  └── Analytics Ready (Performance Metrics)                 │
└─────────────────────────────────────────────────────────────┘
```

### Technology Stack

| Category | Technology | Purpose |
|----------|------------|---------|
| **Frontend** | React 18 + TypeScript | Component-based UI with type safety |
| **Build Tool** | Vite 5.4+ | Fast development and optimized builds |
| **Styling** | TailwindCSS + shadcn/ui | Utility-first CSS with premium components |
| **Routing** | React Router DOM | Client-side navigation |
| **State** | TanStack Query + Hooks | Server state and local state management |
| **Maps** | Google Maps API | Interactive location display |
| **Performance** | Custom hooks + monitoring | Core Web Vitals tracking |
| **Testing** | Jest + Playwright | Unit and E2E testing |

### Getting Started

#### Quick Start

1. **Clone and Install**
   ```bash
   git clone https://github.com/nicholasg-dev/Ice-Box-Hockey.git
   cd Ice-Box-Hockey
   npm install
   # or for better performance
   pnpm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env.local
   # Add your Google Maps API key to .env.local
   echo "VITE_GOOGLE_MAPS_API_KEY=your_api_key_here" >> .env.local
   ```

3. **Development**
   ```bash
   npm run dev
   # Server starts at http://localhost:8080
   ```

#### Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Create optimized production build |
| `npm run build:dev` | Create development build for debugging |
| `npm run preview` | Preview production build locally |
| `npm run test` | Run unit tests with Jest |
| `npm run test:watch` | Run tests in watch mode |
| `npm run test:coverage` | Generate test coverage report |
| `npm run lint` | Lint code with ESLint |

#### Performance Optimization

The build process includes several optimizations:

```bash
npm run build
# Output example:
# dist/assets/vendor-[hash].js     161KB → 52KB gzipped
# dist/assets/ui-[hash].js         38KB → 14KB gzipped  
# dist/assets/utils-[hash].js      20KB → 7KB gzipped
# dist/assets/icons-[hash].js      7KB → 2KB gzipped
```

See [PERFORMANCE.md](./PERFORMANCE.md) for detailed optimization guide.

## Deployment

### Recommended Platforms

| Platform | Pros | Best For |
|----------|------|----------|
| **Vercel** | Zero-config, automatic optimization, edge functions | Production deployment |
| **Netlify** | Form handling, split testing, edge functions | Marketing sites |
| **Cloudflare Pages** | Global CDN, fast builds, R2 storage | High-traffic sites |
| **AWS Amplify** | Full-stack capabilities, CI/CD integration | Enterprise solutions |

### Deployment Configuration

```bash
# Build for production
npm run build

# Preview build locally
npm run preview

# Deploy to Vercel (recommended)
npx vercel --prod

# Deploy to Netlify
npx netlify deploy --prod --dir=dist
```

### Environment Variables for Production

```bash
# Required
VITE_GOOGLE_MAPS_API_KEY=your_production_api_key

# Optional (Analytics)
VITE_GA_TRACKING_ID=your_google_analytics_id
VITE_HOTJAR_ID=your_hotjar_id
```

## Testing & Quality Assurance

### Testing Strategy

```bash
# Unit Tests (Jest)
npm run test
npm run test:coverage

# E2E Tests (Playwright)
npx playwright test

# Performance Testing
npm run build
npx lighthouse http://localhost:8080 --view

# Accessibility Testing
npx axe-cli http://localhost:8080
```

### Quality Metrics

- **Test Coverage**: > 80% for critical components
- **Performance Score**: > 90 (Lighthouse)
- **Accessibility**: WCAG 2.1 AA compliance
- **SEO Score**: > 95 (Lighthouse)
- **Best Practices**: > 95 (Lighthouse)

## Contributing

Contributions to improve The Ice Box website are welcome. Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Technology Stack

### Core Technologies
- **[React 18](https://reactjs.org/)** - Component-based UI library with concurrent features
- **[TypeScript 5.5+](https://www.typescriptlang.org/)** - Type-safe JavaScript development
- **[Vite 5.4+](https://vitejs.dev/)** - Next-generation frontend build tool
- **[TailwindCSS](https://tailwindcss.com/)** - Utility-first CSS framework

### UI & Components
- **[shadcn/ui](https://ui.shadcn.com/)** - High-quality, accessible component library
- **[Radix UI](https://www.radix-ui.com/)** - Unstyled, accessible UI primitives
- **[Lucide React](https://lucide.dev/)** - Beautiful, customizable icons
- **[Class Variance Authority](https://cva.style/)** - Component variant management

### State & Data Management
- **[TanStack Query](https://tanstack.com/query)** - Powerful data synchronization
- **[React Router DOM](https://reactrouter.com/)** - Declarative client-side routing
- **[React Hook Form](https://react-hook-form.com/)** - Performant form handling
- **[Zod](https://zod.dev/)** - TypeScript-first schema validation

### Development & Build Tools
- **[ESLint](https://eslint.org/)** - Code linting and quality enforcement
- **[Jest](https://jestjs.io/)** - JavaScript testing framework
- **[Playwright](https://playwright.dev/)** - End-to-end testing
- **[PostCSS](https://postcss.org/)** - CSS transformation and optimization

### External Integrations
- **[Google Maps API](https://developers.google.com/maps)** - Interactive location mapping
- **[Google Analytics](https://analytics.google.com/)** - Web analytics (ready)
- **Social Media APIs** - Facebook and Instagram integration

## Project Status & Roadmap

### Current Status: ✅ Production Ready
- **Performance**: Optimized for Core Web Vitals
- **Accessibility**: WCAG 2.1 AA compliant
- **Browser Support**: Modern browsers (95%+ coverage)
- **Mobile**: Fully responsive design
- **SEO**: Optimized meta tags and structure

### Upcoming Features
- [ ] **E-commerce Integration**: Product catalog and shopping cart
- [ ] **User Accounts**: Customer profiles and order history
- [ ] **Inventory Management**: Real-time stock tracking
- [ ] **Appointment Booking**: Equipment fitting appointments
- [ ] **Blog/News Section**: Hockey tips and store updates
- [ ] **Multi-language Support**: Spanish language option

### Performance Benchmarks
- **Lighthouse Score**: 95+ across all metrics
- **Bundle Size**: < 300KB total, < 100KB initial load
- **Load Time**: < 2.5s LCP on 3G networks
- **Accessibility**: 100% WCAG 2.1 AA compliance

## Contact & Support

### The Ice Box Store
- **Address**: 23770 S Western Ave, Harbor City, CA 90710
- **Email**: <EMAIL>
- **Social**: [@iceboxhockey_ca](https://instagram.com/iceboxhockey_ca)
- **Opening**: August 2025

### Development Team
For technical inquiries or contributions, please open an issue on GitHub.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ for the hockey community by The Ice Box team**
