// vite.config.ts
import { defineConfig, loadEnv } from "file:///Users/<USER>/Development/Ice-Box-Hockey/node_modules/vite/dist/node/index.js";
import react from "file:///Users/<USER>/Development/Ice-Box-Hockey/node_modules/@vitejs/plugin-react-swc/index.mjs";
import { fileURLToPath } from "url";
import { dirname } from "path";
var __vite_injected_original_import_meta_url = "file:///Users/<USER>/Development/Ice-Box-Hockey/vite.config.ts";
var __filename = fileURLToPath(__vite_injected_original_import_meta_url);
var __dirname = dirname(__filename);
var vite_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  return {
    server: {
      host: "::",
      port: 8080
    },
    plugins: [
      react({
        // Enable React Fast Refresh
        fastRefresh: true
      })
    ],
    resolve: {
      alias: {
        "@": new URL("./src", __vite_injected_original_import_meta_url).pathname
      }
    },
    build: {
      // Optimize build performance
      target: "esnext",
      minify: "esbuild",
      sourcemap: false,
      rollupOptions: {
        output: {
          // Manual chunk splitting for better caching
          manualChunks: {
            vendor: ["react", "react-dom", "react-router-dom"],
            ui: ["@radix-ui/react-accordion", "@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu"],
            utils: ["clsx", "tailwind-merge", "class-variance-authority"],
            icons: ["lucide-react"]
          }
        }
      },
      // Increase chunk size warning limit
      chunkSizeWarningLimit: 1e3
    },
    optimizeDeps: {
      // Pre-bundle dependencies for faster dev server startup
      include: [
        "react",
        "react-dom",
        "react-router-dom",
        "lucide-react",
        "@tanstack/react-query"
      ]
    },
    define: {
      "import.meta.env": {
        ...env,
        // Expose Google Maps API key
        VITE_GOOGLE_MAPS_API_KEY: JSON.stringify(env.VITE_GOOGLE_MAPS_API_KEY || ""),
        // Expose Google Analytics configuration
        VITE_GA_MEASUREMENT_ID: JSON.stringify(env.VITE_GA_MEASUREMENT_ID || "G-VL34HQ8M9H"),
        VITE_GA_DEBUG: JSON.stringify(env.VITE_GA_DEBUG || "false")
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
