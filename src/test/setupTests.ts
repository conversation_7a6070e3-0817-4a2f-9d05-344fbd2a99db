import '@testing-library/jest-dom';
/// <reference path="./mocks/google-maps-mock.d.ts" />

// Mock the Google Maps API with proper types
interface MockGeocoder {
  geocode: jest.MockedFunction<(request: google.maps.GeocoderRequest, callback: (results: google.maps.GeocoderResult[], status: google.maps.GeocoderStatus) => void) => void>;
}

interface MockMarker {
  setMap: jest.MockedFunction<(map: google.maps.Map | null) => void>;
  setPosition: jest.MockedFunction<(latLng: google.maps.LatLng | google.maps.LatLngLiteral | null) => void>;
}

interface MockMap {
  setCenter: jest.MockedFunction<(latlng: google.maps.LatLng | google.maps.LatLngLiteral) => void>;
  setZoom: jest.MockedFunction<(zoom: number) => void>;
}

const mockGeocoder: MockGeocoder = {
  geocode: jest.fn(),
};

const mockMarker: MockMarker = {
  setMap: jest.fn(),
  setPosition: jest.fn(),
};

const mockMap: MockMap = {
  setCenter: jest.fn(),
  setZoom: jest.fn(),
};

// Mock the Google Maps API with proper type definitions
const mockGoogleMaps = {
  maps: {
    Map: jest.fn().mockImplementation(() => mockMap),
    Marker: jest.fn().mockImplementation(() => mockMarker),
    Geocoder: jest.fn().mockImplementation(() => mockGeocoder),
    GeocoderStatus: {
      OK: 'OK' as google.maps.GeocoderStatus,
      ZERO_RESULTS: 'ZERO_RESULTS' as google.maps.GeocoderStatus,
      ERROR: 'ERROR' as google.maps.GeocoderStatus,
      INVALID_REQUEST: 'INVALID_REQUEST' as google.maps.GeocoderStatus,
      OVER_QUERY_LIMIT: 'OVER_QUERY_LIMIT' as google.maps.GeocoderStatus,
      REQUEST_DENIED: 'REQUEST_DENIED' as google.maps.GeocoderStatus,
      UNKNOWN_ERROR: 'UNKNOWN_ERROR' as google.maps.GeocoderStatus,
    },
    event: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addDomListener: jest.fn(),
      addDomListenerOnce: jest.fn(),
      addListenerOnce: jest.fn(),
      clearInstanceListeners: jest.fn(),
      clearListeners: jest.fn(),
      trigger: jest.fn(),
    },
  },
};

// Type assertion for global object with proper Google Maps typing
const globalWithGoogle = global as typeof global & {
  google: typeof google;
};

globalWithGoogle.google = mockGoogleMaps as unknown as typeof google;

// Mock the window.initMap function with proper typing
const windowWithInitMap = window as typeof window & {
  initMap: jest.MockedFunction<() => void>;
};

windowWithInitMap.initMap = jest.fn();
