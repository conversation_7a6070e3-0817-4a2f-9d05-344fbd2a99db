// This file provides TypeScript types for our Google Maps mocks

declare namespace google.maps {
  class Map {
    constructor(container: HTMLElement, options?: MapOptions);
    setCenter(latlng: LatLng | LatLngLiteral): void;
    setZoom(zoom: number): void;
  }

  class Marker {
    constructor(options?: MarkerOptions);
    setMap(map: Map | null): void;
    setPosition(latLng: LatLng | LatLngLiteral | null): void;
  }

  class Geocoder {
    geocode(
      request: GeocoderRequest,
      callback: (results: GeocoderResult[], status: GeocoderStatus) => void
    ): void;
  }

  enum GeocoderStatus {
    ERROR = 'ERROR',
    INVALID_REQUEST = 'INVALID_REQUEST',
    OK = 'OK',
    OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
    REQUEST_DENIED = 'REQUEST_DENIED',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR',
    ZERO_RESULTS = 'ZERO_RESULTS',
  }

  interface GeocoderRequest {
    address?: string;
    location?: LatLng | LatLngLiteral;
  }

  interface GeocoderResult {
    geometry: {
      location: LatLng;
    };
    formatted_address: string;
  }

  interface MapOptions {
    center?: LatLng | LatLngLiteral;
    zoom?: number;
    mapTypeId?: MapTypeId;
    backgroundColor?: string;
    clickableIcons?: boolean;
    disableDefaultUI?: boolean;
    disableDoubleClickZoom?: boolean;
    draggable?: boolean;
    draggableCursor?: string;
    draggingCursor?: string;
    fullscreenControl?: boolean;
    fullscreenControlOptions?: FullscreenControlOptions;
    gestureHandling?: GestureHandlingOptions;
    heading?: number;
    keyboardShortcuts?: boolean;
    mapTypeControl?: boolean;
    mapTypeControlOptions?: MapTypeControlOptions;
    maxZoom?: number;
    minZoom?: number;
    noClear?: boolean;
    panControl?: boolean;
    panControlOptions?: PanControlOptions;
    restriction?: MapRestriction;
    rotateControl?: boolean;
    rotateControlOptions?: RotateControlOptions;
    scaleControl?: boolean;
    scaleControlOptions?: ScaleControlOptions;
    scrollwheel?: boolean;
    streetView?: StreetViewPanorama;
    streetViewControl?: boolean;
    streetViewControlOptions?: StreetViewControlOptions;
    styles?: MapTypeStyle[];
    tilt?: number;
    zoomControl?: boolean;
    zoomControlOptions?: ZoomControlOptions;
  }

  enum MapTypeId {
    HYBRID = 'hybrid',
    ROADMAP = 'roadmap',
    SATELLITE = 'satellite',
    TERRAIN = 'terrain',
  }

  type GestureHandlingOptions = 'cooperative' | 'greedy' | 'none' | 'auto';

  interface FullscreenControlOptions {
    position?: ControlPosition;
  }

  interface MapTypeControlOptions {
    mapTypeIds?: (MapTypeId | string)[];
    position?: ControlPosition;
    style?: MapTypeControlStyle;
  }

  interface PanControlOptions {
    position?: ControlPosition;
  }

  interface MapRestriction {
    latLngBounds: LatLngBounds;
    strictBounds?: boolean;
  }

  interface RotateControlOptions {
    position?: ControlPosition;
  }

  interface ScaleControlOptions {
    style?: ScaleControlStyle;
  }

  interface StreetViewControlOptions {
    position?: ControlPosition;
  }

  interface ZoomControlOptions {
    position?: ControlPosition;
    style?: ZoomControlStyle;
  }

  interface MapTypeStyle {
    elementType?: string;
    featureType?: string;
    stylers: MapTypeStyler[];
  }

  interface MapTypeStyler {
    color?: string;
    gamma?: number;
    hue?: string;
    invert_lightness?: boolean;
    lightness?: number;
    saturation?: number;
    visibility?: string;
    weight?: number;
  }

  interface LatLngBounds {
    contains(latLng: LatLng | LatLngLiteral): boolean;
    equals(other: LatLngBounds): boolean;
    extend(point: LatLng | LatLngLiteral): LatLngBounds;
    getCenter(): LatLng;
    getNorthEast(): LatLng;
    getSouthWest(): LatLng;
    intersects(other: LatLngBounds): boolean;
    isEmpty(): boolean;
    toJSON(): LatLngBoundsLiteral;
    toString(): string;
    toUrlValue(precision?: number): string;
    union(other: LatLngBounds): LatLngBounds;
  }

  interface LatLngBoundsLiteral {
    east: number;
    north: number;
    south: number;
    west: number;
  }

  interface StreetViewPanorama {
    // Simplified interface for street view
    getPosition(): LatLng;
    setPosition(latLng: LatLng | LatLngLiteral): void;
  }

  enum ControlPosition {
    BOTTOM_CENTER = 11,
    BOTTOM_LEFT = 10,
    BOTTOM_RIGHT = 12,
    LEFT_BOTTOM = 6,
    LEFT_CENTER = 4,
    LEFT_TOP = 5,
    RIGHT_BOTTOM = 9,
    RIGHT_CENTER = 8,
    RIGHT_TOP = 7,
    TOP_CENTER = 2,
    TOP_LEFT = 1,
    TOP_RIGHT = 3,
  }

  enum MapTypeControlStyle {
    DEFAULT = 0,
    DROPDOWN_MENU = 2,
    HORIZONTAL_BAR = 1,
  }

  enum ScaleControlStyle {
    DEFAULT = 0,
  }

  enum ZoomControlStyle {
    DEFAULT = 0,
    LARGE = 1,
    SMALL = 2,
  }

  interface MarkerOptions {
    position?: LatLng | LatLngLiteral;
    map?: Map;
    title?: string;
    label?: string | MarkerLabel;
    icon?: string | Icon | Symbol;
    shape?: MarkerShape;
    cursor?: string;
    clickable?: boolean;
    draggable?: boolean;
    optimized?: boolean;
    visible?: boolean;
    zIndex?: number;
    opacity?: number;
    animation?: Animation;
  }

  interface MarkerLabel {
    text: string;
    color?: string;
    fontFamily?: string;
    fontSize?: string;
    fontWeight?: string;
    className?: string;
  }

  interface Icon {
    url: string;
    size?: Size;
    origin?: Point;
    anchor?: Point;
    scaledSize?: Size;
    labelOrigin?: Point;
  }

  interface Symbol {
    path: string | SymbolPath;
    anchor?: Point;
    fillColor?: string;
    fillOpacity?: number;
    labelOrigin?: Point;
    rotation?: number;
    scale?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    strokeWeight?: number;
  }

  interface MarkerShape {
    coords: number[];
    type: string;
  }

  interface Size {
    width: number;
    height: number;
    widthUnit?: string;
    heightUnit?: string;
  }

  interface Point {
    x: number;
    y: number;
  }

  enum Animation {
    BOUNCE = 1,
    DROP = 2,
  }

  enum SymbolPath {
    BACKWARD_CLOSED_ARROW = 3,
    BACKWARD_OPEN_ARROW = 4,
    CIRCLE = 0,
    FORWARD_CLOSED_ARROW = 1,
    FORWARD_OPEN_ARROW = 2,
  }

  interface LatLng {
    lat(): number;
    lng(): number;
    toJSON(): { lat: number; lng: number };
    toUrlValue(): string;
  }

  interface LatLngLiteral {
    lat: number;
    lng: number;
  }

  const event: {
    addListener: jest.Mock;
    removeListener: jest.Mock;
    addDomListener: jest.Mock;
    addDomListenerOnce: jest.Mock;
    addListenerOnce: jest.Mock;
    clearInstanceListeners: jest.Mock;
    clearListeners: jest.Mock;
    trigger: jest.Mock;
  };

  const maps: {
    Map: jest.Mock<Map, [HTMLElement, MapOptions?]>;
    Marker: jest.Mock<Marker, [MarkerOptions?]>;
    Geocoder: jest.Mock<Geocoder>;
    GeocoderStatus: typeof GeocoderStatus;
    event: typeof event;
    LatLng: new (lat: number, lng: number) => LatLng;
  };
}

declare const google: {
  maps: typeof google.maps;
};

declare global {
  interface Window {
    google: typeof google;
    initMap: () => void;
  }
}

export {};
