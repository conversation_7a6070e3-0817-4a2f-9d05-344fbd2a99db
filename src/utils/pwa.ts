/**
 * PWA utilities for service worker registration and install prompts
 */

// PWA Utilities for The Ice Box Hockey
// Handles service worker registration, install prompts, and PWA features

// Global gtag function type declaration
declare global {
  interface Window {
    gtag?: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string | Date,
      config?: Record<string, any>
    ) => void;
  }
}

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

// Service Worker registration
export const registerServiceWorker = async (): Promise<void> => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });
      
      console.log('Service Worker registered successfully:', registration);
      
      // Handle updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available, show update notification
              showUpdateNotification();
            }
          });
        }
      });
      
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }
};

// PWA Install Prompt
let deferredPrompt: any = null;

export const initializePWAPrompt = (): void => {
  // Listen for the beforeinstallprompt event
  window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault();
    // Stash the event so it can be triggered later
    deferredPrompt = e;
    // Show install button
    showInstallButton();
  });
  
  // Listen for app installed event
  window.addEventListener('appinstalled', () => {
    console.log('PWA was installed');
    hideInstallButton();
    // Track installation
    if (window.gtag) {
      window.gtag('event', 'pwa_install', {
        event_category: 'engagement',
        event_label: 'app_installed'
      });
    }
  });
};

export const showInstallPrompt = async (): Promise<boolean> => {
  if (!deferredPrompt) {
    return false;
  }
  
  // Show the install prompt
  deferredPrompt.prompt();
  
  // Wait for the user to respond to the prompt
  const { outcome } = await deferredPrompt.userChoice;
  
  // Track user choice
  if (window.gtag) {
    window.gtag('event', 'pwa_install_prompt', {
      event_category: 'engagement',
      event_label: outcome
    });
  }
  
  // Clear the deferredPrompt
  deferredPrompt = null;
  
  return outcome === 'accepted';
};

// Check if app is installed
export const isPWAInstalled = (): boolean => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         (window.navigator as any).standalone === true;
};

// Show install button (to be implemented in UI)
const showInstallButton = (): void => {
  const event = new CustomEvent('pwa:showInstall');
  window.dispatchEvent(event);
};

// Hide install button
const hideInstallButton = (): void => {
  const event = new CustomEvent('pwa:hideInstall');
  window.dispatchEvent(event);
};

// Show update notification
const showUpdateNotification = (): void => {
  const event = new CustomEvent('pwa:updateAvailable');
  window.dispatchEvent(event);
};

// Network status detection
export const getNetworkStatus = (): { online: boolean; effectiveType?: string } => {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  
  return {
    online: navigator.onLine,
    effectiveType: connection?.effectiveType || 'unknown'
  };
};

// Listen for network changes
export const initializeNetworkListener = (): void => {
  window.addEventListener('online', () => {
    console.log('App is online');
    const event = new CustomEvent('pwa:online');
    window.dispatchEvent(event);
  });
  
  window.addEventListener('offline', () => {
    console.log('App is offline');
    const event = new CustomEvent('pwa:offline');
    window.dispatchEvent(event);
  });
};

// PWA Analytics tracking
export const trackPWAMetrics = (): void => {
  if (window.gtag) {
    // Track PWA usage
    window.gtag('event', 'pwa_usage', {
      event_category: 'engagement',
      event_label: isPWAInstalled() ? 'installed' : 'browser',
      custom_parameter_1: 'pwa_mode'
    });
    
    // Track network status
    const networkStatus = getNetworkStatus();
    window.gtag('event', 'network_status', {
      event_category: 'technical',
      event_label: networkStatus.online ? 'online' : 'offline',
      custom_parameter_2: networkStatus.effectiveType
    });
  }
};