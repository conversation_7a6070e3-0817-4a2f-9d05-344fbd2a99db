/**
 * Google Analytics 4 Integration for The Ice Box Hockey
 * 
 * Stream Details:
 * - Stream Name: iceboxhockey.com
 * - Stream URL: https://iceboxhockey.com
 * - Stream ID: 11376792355
 * - Measurement ID: G-VL34HQ8M9H
 */

// Extend the Window interface to include gtag
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string | Date,
      config?: Record<string, any>
    ) => void;
    dataLayer: any[];
  }
}

// Analytics configuration
const GA_MEASUREMENT_ID = import.meta.env.VITE_GA_MEASUREMENT_ID || 'G-VL34HQ8M9H';
const GA_DEBUG = import.meta.env.VITE_GA_DEBUG === 'true';
const IS_PRODUCTION = import.meta.env.PROD;

/**
 * Initialize Google Analytics
 * Only runs in production unless debug mode is enabled
 */
export const initializeAnalytics = (): void => {
  if (!IS_PRODUCTION && !GA_DEBUG) {
    console.log('Analytics disabled in development mode');
    return;
  }

  if (typeof window === 'undefined') {
    return;
  }

  // Initialize dataLayer if it doesn't exist
  window.dataLayer = window.dataLayer || [];
  
  // Initialize gtag function
  window.gtag = function() {
    window.dataLayer.push(arguments);
  };

  // Configure Google Analytics
  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    // Enhanced measurement settings
    send_page_view: true,
    allow_google_signals: true,
    allow_ad_personalization_signals: false, // Privacy-focused
    
    // Custom configuration for The Ice Box
    custom_map: {
      'custom_parameter_1': 'store_section',
      'custom_parameter_2': 'user_type'
    },
    
    // Debug mode for development
    debug_mode: GA_DEBUG
  });

  console.log('Google Analytics initialized:', GA_MEASUREMENT_ID);
};

/**
 * Track page views
 */
export const trackPageView = (pagePath: string, pageTitle?: string): void => {
  if (!IS_PRODUCTION && !GA_DEBUG) return;
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'page_view', {
      page_path: pagePath,
      page_title: pageTitle || document.title,
      page_location: window.location.href
    });
    
    console.log('Page view tracked:', pagePath);
  }
};

/**
 * Track custom events
 */
export const trackEvent = (
  eventName: string,
  parameters: Record<string, any> = {}
): void => {
  if (!IS_PRODUCTION && !GA_DEBUG) return;
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      ...parameters,
      // Add default parameters
      timestamp: new Date().toISOString(),
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`
    });
    
    console.log('Event tracked:', eventName, parameters);
  }
};

/**
 * Pre-defined event tracking functions for The Ice Box
 */

// Navigation and engagement events
export const trackNavigation = (section: string, method: 'click' | 'scroll' = 'click'): void => {
  trackEvent('navigate_to_section', {
    section_name: section,
    navigation_method: method,
    event_category: 'navigation'
  });
};

// Store information interactions
export const trackStoreInteraction = (action: string, details?: Record<string, any>): void => {
  trackEvent('store_interaction', {
    interaction_type: action,
    event_category: 'store_info',
    ...details
  });
};

// Map interactions
export const trackMapInteraction = (action: 'view' | 'click' | 'directions'): void => {
  trackEvent('map_interaction', {
    interaction_type: action,
    event_category: 'location',
    store_location: '23770 S Western Ave, Harbor City, CA 90710'
  });
};

// Brand interactions
export const trackBrandClick = (brandName: string, brandUrl: string): void => {
  trackEvent('brand_click', {
    brand_name: brandName,
    brand_url: brandUrl,
    event_category: 'brands',
    outbound_link: true
  });
};

// Team sales interactions
export const trackTeamSalesInteraction = (action: string, productName?: string): void => {
  trackEvent('team_sales_interaction', {
    interaction_type: action,
    product_name: productName,
    event_category: 'team_sales'
  });
};

// Service inquiries
export const trackServiceInquiry = (serviceType: string): void => {
  trackEvent('service_inquiry', {
    service_type: serviceType,
    event_category: 'services',
    value: 1 // Potential lead value
  });
};

// Contact interactions
export const trackContactInteraction = (method: 'email' | 'phone' | 'social'): void => {
  trackEvent('contact_interaction', {
    contact_method: method,
    event_category: 'contact'
  });
};

// Social media clicks
export const trackSocialClick = (platform: 'facebook' | 'instagram'): void => {
  trackEvent('social_media_click', {
    social_platform: platform,
    event_category: 'social_media',
    outbound_link: true
  });
};

// Performance tracking
export const trackPerformanceMetric = (metricName: string, value: number, unit: string): void => {
  trackEvent('performance_metric', {
    metric_name: metricName,
    metric_value: value,
    metric_unit: unit,
    event_category: 'performance'
  });
};

// Error tracking
export const trackError = (errorType: string, errorMessage: string, errorLocation?: string): void => {
  trackEvent('error_occurred', {
    error_type: errorType,
    error_message: errorMessage,
    error_location: errorLocation,
    event_category: 'errors'
  });
};

// Conversion tracking
export const trackConversion = (conversionType: string, value?: number): void => {
  trackEvent('conversion', {
    conversion_type: conversionType,
    conversion_value: value,
    event_category: 'conversions'
  });
};

/**
 * Enhanced ecommerce tracking (for future use)
 */
export const trackPurchase = (transactionId: string, items: any[], value: number): void => {
  trackEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: 'USD',
    items: items
  });
};

export const trackAddToCart = (itemId: string, itemName: string, price: number): void => {
  trackEvent('add_to_cart', {
    currency: 'USD',
    value: price,
    items: [{
      item_id: itemId,
      item_name: itemName,
      price: price,
      quantity: 1
    }]
  });
};

/**
 * User identification (for future user accounts)
 */
export const setUserId = (userId: string): void => {
  if (!IS_PRODUCTION && !GA_DEBUG) return;
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      user_id: userId
    });
  }
};

/**
 * Set custom user properties
 */
export const setUserProperties = (properties: Record<string, any>): void => {
  if (!IS_PRODUCTION && !GA_DEBUG) return;
  
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('set', 'user_properties', properties);
  }
};

export default {
  initialize: initializeAnalytics,
  trackPageView,
  trackEvent,
  trackNavigation,
  trackStoreInteraction,
  trackMapInteraction,
  trackBrandClick,
  trackTeamSalesInteraction,
  trackServiceInquiry,
  trackContactInteraction,
  trackSocialClick,
  trackPerformanceMetric,
  trackError,
  trackConversion,
  trackPurchase,
  trackAddToCart,
  setUserId,
  setUserProperties
};