// This file ensures the Google Maps API key is available before the app loads
// It's imported in main.tsx before the app initializes

// Get the API key from environment variables
const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

// Extend the Window interface to include our custom property
declare global {
  interface Window {
    GOOGLE_MAPS_API_KEY?: string;
  }
}

// Make it available globally
if (apiKey) {
  window.GOOGLE_MAPS_API_KEY = apiKey;
  console.log('Google Maps API key loaded');
} else {
  console.warn('Google Maps API key not found in environment variables');
  console.warn('Please set VITE_GOOGLE_MAPS_API_KEY in your .env file');
}

export {};
