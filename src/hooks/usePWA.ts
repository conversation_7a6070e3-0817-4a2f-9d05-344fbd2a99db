import { useState, useEffect, useCallback } from 'react';
import {
  registerService<PERSON><PERSON><PERSON>,
  initializePWAPrompt,
  initializeNetworkListener,
  showInstallPrompt,
  isPWAInstalled,
  getNetworkStatus,
  trackPWAMetrics
} from '@/utils/pwa';

interface PWAState {
  isInstalled: boolean;
  isOnline: boolean;
  canInstall: boolean;
  isLoading: boolean;
  networkType: string;
  updateAvailable: boolean;
}

interface PWAActions {
  install: () => Promise<boolean>;
  refresh: () => void;
  dismissUpdate: () => void;
}

export const usePWA = (): PWAState & PWAActions => {
  const [state, setState] = useState<PWAState>({
    isInstalled: false,
    isOnline: true,
    canInstall: false,
    isLoading: true,
    networkType: 'unknown',
    updateAvailable: false
  });

  // Initialize PWA functionality
  useEffect(() => {
    const initializePWA = async () => {
      try {
        // Register service worker
        await registerServiceWorker();
        
        // Initialize install prompt
        initializePWAPrompt();
        
        // Initialize network listener
        initializeNetworkListener();
        
        // Get initial state
        const networkStatus = getNetworkStatus();
        const installed = isPWAInstalled();
        
        setState(prev => ({
          ...prev,
          isInstalled: installed,
          isOnline: networkStatus.online,
          networkType: networkStatus.effectiveType || 'unknown',
          isLoading: false
        }));
        
        // Track PWA metrics
        trackPWAMetrics();
        
      } catch (error) {
        console.error('PWA initialization failed:', error);
        setState(prev => ({ ...prev, isLoading: false }));
      }
    };

    initializePWA();
  }, []);

  // Listen for PWA events
  useEffect(() => {
    const handleShowInstall = () => {
      setState(prev => ({ ...prev, canInstall: true }));
    };

    const handleHideInstall = () => {
      setState(prev => ({ ...prev, canInstall: false }));
    };

    const handleUpdateAvailable = () => {
      setState(prev => ({ ...prev, updateAvailable: true }));
    };

    const handleOnline = () => {
      const networkStatus = getNetworkStatus();
      setState(prev => ({
        ...prev,
        isOnline: true,
        networkType: networkStatus.effectiveType || 'unknown'
      }));
    };

    const handleOffline = () => {
      setState(prev => ({
        ...prev,
        isOnline: false
      }));
    };

    // Add event listeners
    window.addEventListener('pwa:showInstall', handleShowInstall);
    window.addEventListener('pwa:hideInstall', handleHideInstall);
    window.addEventListener('pwa:updateAvailable', handleUpdateAvailable);
    window.addEventListener('pwa:online', handleOnline);
    window.addEventListener('pwa:offline', handleOffline);

    return () => {
      window.removeEventListener('pwa:showInstall', handleShowInstall);
      window.removeEventListener('pwa:hideInstall', handleHideInstall);
      window.removeEventListener('pwa:updateAvailable', handleUpdateAvailable);
      window.removeEventListener('pwa:online', handleOnline);
      window.removeEventListener('pwa:offline', handleOffline);
    };
  }, []);

  // Install app
  const install = useCallback(async (): Promise<boolean> => {
    try {
      const result = await showInstallPrompt();
      if (result) {
        setState(prev => ({
          ...prev,
          isInstalled: true,
          canInstall: false
        }));
      }
      return result;
    } catch (error) {
      console.error('Install failed:', error);
      return false;
    }
  }, []);

  // Refresh app (for updates)
  const refresh = useCallback(() => {
    window.location.reload();
  }, []);

  // Dismiss update notification
  const dismissUpdate = useCallback(() => {
    setState(prev => ({ ...prev, updateAvailable: false }));
  }, []);

  return {
    ...state,
    install,
    refresh,
    dismissUpdate
  };
};

export default usePWA;