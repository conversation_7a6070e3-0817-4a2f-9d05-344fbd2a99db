import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { trackPageView, trackNavigation } from '@/utils/analytics';

/**
 * Hook to automatically track page views and navigation
 */
export const useAnalytics = () => {
  const location = useLocation();

  useEffect(() => {
    // Track page view when route changes
    const pagePath = location.pathname + location.search;
    const pageTitle = getPageTitle(location.pathname);
    
    trackPageView(pagePath, pageTitle);
  }, [location]);

  return {
    trackNavigation,
    trackPageView
  };
};

/**
 * Get page title based on route
 */
const getPageTitle = (pathname: string): string => {
  const titles: Record<string, string> = {
    '/': 'The Ice Box - Hockey Equipment Store',
    '/teamsales': 'Team Sales - Bay Harbor Red Wings | The Ice Box',
    '/404': 'Page Not Found | The Ice Box'
  };

  return titles[pathname] || 'The Ice Box - Hockey Equipment Store';
};

export default useAnalytics;