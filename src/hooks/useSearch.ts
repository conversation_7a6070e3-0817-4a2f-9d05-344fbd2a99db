import { useState, useMemo, useCallback } from 'react';
import { trackEvent } from '@/utils/analytics';

interface SearchOptions {
  keys: string[];
  threshold?: number;
  includeScore?: boolean;
  includeMatches?: boolean;
}

interface SearchResult<T> {
  item: T;
  score?: number;
  matches?: Array<{
    indices: number[][];
    value: string;
    key: string;
  }>;
}

// Simple fuzzy search implementation
function fuzzySearch<T>(
  items: T[],
  query: string,
  options: SearchOptions
): SearchResult<T>[] {
  if (!query.trim()) return items.map(item => ({ item }));

  const { keys, threshold = 0.6 } = options;
  const results: SearchResult<T>[] = [];

  items.forEach(item => {
    let bestScore = 0;
    let bestMatches: any[] = [];

    keys.forEach(key => {
      const value = getNestedValue(item, key);
      if (typeof value === 'string') {
        const score = calculateScore(value.toLowerCase(), query.toLowerCase());
        if (score > bestScore) {
          bestScore = score;
          if (options.includeMatches) {
            bestMatches = [{ key, value, indices: [] }];
          }
        }
      }
    });

    if (bestScore >= threshold) {
      const result: SearchResult<T> = { item };
      if (options.includeScore) result.score = bestScore;
      if (options.includeMatches) result.matches = bestMatches;
      results.push(result);
    }
  });

  return results.sort((a, b) => (b.score || 0) - (a.score || 0));
}

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

function calculateScore(text: string, query: string): number {
  if (text.includes(query)) {
    return 1 - (text.length - query.length) / text.length;
  }
  
  // Simple character matching
  let matches = 0;
  let queryIndex = 0;
  
  for (let i = 0; i < text.length && queryIndex < query.length; i++) {
    if (text[i] === query[queryIndex]) {
      matches++;
      queryIndex++;
    }
  }
  
  return matches / query.length;
}

export interface UseSearchProps<T> {
  items: T[];
  searchKeys: string[];
  threshold?: number;
  debounceMs?: number;
}

export const useSearch = <T>({
  items,
  searchKeys,
  threshold = 0.3,
  debounceMs = 300
}: UseSearchProps<T>) => {
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');

  // Debounce search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
      
      // Track search analytics
      if (query.trim()) {
        trackEvent('search_query', {
          query: query.trim(),
          results_count: results.length,
          event_category: 'search'
        });
      }
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [query, debounceMs]);

  const results = useMemo(() => {
    return fuzzySearch(items, debouncedQuery, {
      keys: searchKeys,
      threshold,
      includeScore: true,
      includeMatches: true
    });
  }, [items, debouncedQuery, searchKeys, threshold]);

  const clearSearch = useCallback(() => {
    setQuery('');
    setDebouncedQuery('');
  }, []);

  return {
    query,
    setQuery,
    results: results.map(r => r.item),
    resultsWithScore: results,
    isSearching: query !== debouncedQuery,
    hasQuery: debouncedQuery.trim().length > 0,
    clearSearch
  };
};

export default useSearch;