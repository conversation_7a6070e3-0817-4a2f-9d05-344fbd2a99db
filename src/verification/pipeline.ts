/**
 * Main verification pipeline orchestrator
 */

import { 
  VerificationReport, 
  VerificationStage, 
  VerificationStageResult, 
  PipelineOptions,
  BuildVerificationResult,
  PerformanceMetrics,
  AccessibilityResult,
  PWAValidationResult,
  ExternalDependencies
} from './types';
import { ConfigManager } from './config';
import { BuildVerificationEngine } from './build';
import { TestOrchestrator } from './test-orchestrator';
import { TestConfigManager } from './test-config';
import { createPerformanceMonitor } from './performance';
import { createAccessibilityValidator } from './accessibility';
import { chromium } from '@playwright/test';

/**
 * Main verification pipeline class that orchestrates all verification stages
 */
export class VerificationPipeline {
  private configManager: ConfigManager;
  private stages: Map<string, VerificationStage> = new Map();
  private options: PipelineOptions;

  constructor(options: PipelineOptions) {
    this.options = options;
    this.configManager = new ConfigManager(options.config);
    this.initializeStages();
  }

  /**
   * Initialize all verification stages
   */
  private initializeStages(): void {
    // Stages will be implemented in subsequent tasks
    // For now, we create placeholder stages
    this.stages.set('build', new BuildVerificationStage());
    this.stages.set('tests', new TestOrchestrationStage());
    this.stages.set('performance', new PerformanceMonitoringStage(this.configManager));
    this.stages.set('accessibility', new AccessibilityValidationStage(this.configManager));
    this.stages.set('pwa', new PWAValidationStage());
    this.stages.set('dependencies', new DependencyCheckStage());
  }

  /**
   * Execute the complete verification pipeline
   */
  async execute(): Promise<VerificationReport> {
    const startTime = Date.now();
    const report: Partial<VerificationReport> = {
      timestamp: new Date(),
      overallStatus: 'passed',
      recommendations: [],
      deploymentReady: true,
    };

    try {
      this.log('Starting verification pipeline...');

      // Validate configuration
      const configValidation = this.configManager.validateConfig();
      if (!configValidation.valid) {
        throw new Error(`Configuration validation failed: ${configValidation.errors.join(', ')}`);
      }

      // Execute stages in order
      const stageResults = new Map<string, VerificationStageResult>();
      
      for (const [stageName, stage] of this.stages) {
        if (this.options.skipStages?.includes(stageName)) {
          this.log(`Skipping stage: ${stageName}`);
          continue;
        }

        this.log(`Executing stage: ${stageName}`);
        const stageStartTime = Date.now();
        
        try {
          const result = await stage.execute();
          stageResults.set(stageName, result);
          
          const duration = Date.now() - stageStartTime;
          this.log(`Stage ${stageName} completed in ${duration}ms`);

          if (!result.success) {
            report.overallStatus = 'failed';
            report.deploymentReady = false;
            this.log(`Stage ${stageName} failed: ${result.errors.join(', ')}`);
          }

          if (result.warnings.length > 0) {
            if (report.overallStatus === 'passed') {
              report.overallStatus = 'warning';
            }
            this.log(`Stage ${stageName} warnings: ${result.warnings.join(', ')}`);
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          this.log(`Stage ${stageName} threw error: ${errorMessage}`);
          
          stageResults.set(stageName, {
            success: false,
            duration: Date.now() - stageStartTime,
            data: null,
            errors: [errorMessage],
            warnings: [],
          });

          report.overallStatus = 'failed';
          report.deploymentReady = false;
        }
      }

      // Compile final report
      const finalReport = this.compileReport(report, stageResults);
      
      const totalDuration = Date.now() - startTime;
      this.log(`Verification pipeline completed in ${totalDuration}ms with status: ${finalReport.overallStatus}`);

      return finalReport;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.log(`Pipeline execution failed: ${errorMessage}`);
      
      return {
        timestamp: new Date(),
        overallStatus: 'failed',
        buildVerification: this.createEmptyBuildResult(),
        testResults: [],
        performanceMetrics: this.createEmptyPerformanceMetrics(),
        accessibilityResults: this.createEmptyAccessibilityResult(),
        pwaValidation: this.createEmptyPWAResult(),
        dependencyStatus: this.createEmptyDependencyStatus(),
        recommendations: [`Pipeline execution failed: ${errorMessage}`],
        deploymentReady: false,
      };
    }
  }

  /**
   * Compile the final verification report from stage results
   */
  private compileReport(
    baseReport: Partial<VerificationReport>, 
    stageResults: Map<string, VerificationStageResult>
  ): VerificationReport {
    const buildResult = stageResults.get('build');
    const testResult = stageResults.get('tests');
    const performanceResult = stageResults.get('performance');
    const accessibilityResult = stageResults.get('accessibility');
    const pwaResult = stageResults.get('pwa');
    const dependencyResult = stageResults.get('dependencies');

    return {
      timestamp: baseReport.timestamp!,
      overallStatus: baseReport.overallStatus!,
      buildVerification: buildResult?.data || this.createEmptyBuildResult(),
      testResults: testResult?.data || [],
      performanceMetrics: performanceResult?.data || this.createEmptyPerformanceMetrics(),
      accessibilityResults: accessibilityResult?.data || this.createEmptyAccessibilityResult(),
      pwaValidation: pwaResult?.data || this.createEmptyPWAResult(),
      dependencyStatus: dependencyResult?.data || this.createEmptyDependencyStatus(),
      recommendations: this.generateRecommendations(stageResults),
      deploymentReady: baseReport.deploymentReady!,
    };
  }

  /**
   * Generate recommendations based on stage results
   */
  private generateRecommendations(stageResults: Map<string, VerificationStageResult>): string[] {
    const recommendations: string[] = [];

    for (const [stageName, result] of stageResults) {
      if (!result.success) {
        recommendations.push(`Fix ${stageName} issues before deployment`);
      }
      if (result.warnings.length > 0) {
        recommendations.push(`Address ${stageName} warnings for optimal performance`);
      }
    }

    if (recommendations.length === 0) {
      recommendations.push('All verification checks passed - ready for deployment');
    }

    return recommendations;
  }

  /**
   * Log messages based on verbosity setting
   */
  private log(message: string): void {
    if (this.options.verbose) {
      console.log(`[VerificationPipeline] ${message}`);
    }
  }

  // Helper methods to create empty results for failed stages
  private createEmptyBuildResult(): BuildVerificationResult {
    return {
      success: false,
      buildTime: 0,
      errors: [],
      warnings: [],
      outputSize: { total: 0, chunks: [] },
    };
  }

  private createEmptyPerformanceMetrics(): PerformanceMetrics {
    return {
      lcp: 0,
      fid: 0,
      cls: 0,
      fcp: 0,
      lighthouse: { performance: 0, accessibility: 0, bestPractices: 0, seo: 0 },
    };
  }

  private createEmptyAccessibilityResult(): AccessibilityResult {
    return {
      compliant: false,
      violations: [],
      warnings: [],
      testedPages: [],
    };
  }

  private createEmptyPWAResult(): PWAValidationResult {
    return {
      serviceWorkerRegistered: false,
      manifestValid: false,
      offlineFunctionality: false,
      installable: false,
      cacheStrategy: { staticAssetsCache: false, apiResponseCache: false, offlinePages: [] },
    };
  }

  private createEmptyDependencyStatus(): ExternalDependencies {
    return {
      googleMaps: { service: 'Google Maps', available: false, responseTime: 0 },
      cdnResources: [],
      apiEndpoints: [],
    };
  }
}

// Build verification stage implementation
class BuildVerificationStage implements VerificationStage {
  name = 'Build Verification';
  private buildEngine: BuildVerificationEngine;

  constructor() {
    this.buildEngine = new BuildVerificationEngine();
  }

  async execute(): Promise<VerificationStageResult> {
    const startTime = Date.now();
    
    try {
      const buildResult = await this.buildEngine.verify();
      const duration = Date.now() - startTime;

      return {
        success: buildResult.success,
        duration,
        data: buildResult,
        errors: buildResult.success ? [] : this.buildEngine.getDetailedErrors(),
        warnings: this.buildEngine.getDetailedWarnings(),
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        success: false,
        duration,
        data: {
          success: false,
          buildTime: 0,
          errors: [],
          warnings: [],
          outputSize: { total: 0, chunks: [] },
        },
        errors: [errorMessage],
        warnings: [],
      };
    }
  }
}

class TestOrchestrationStage implements VerificationStage {
  name = 'Test Orchestration';
  private testOrchestrator: TestOrchestrator | null = null;

  async execute(): Promise<VerificationStageResult> {
    const startTime = Date.now();
    
    try {
      // Load test configuration
      const testConfig = await TestConfigManager.loadConfig({
        environment: process.env.NODE_ENV as 'development' | 'production' | 'test' || 'production'
      });

      // Initialize test orchestrator
      this.testOrchestrator = new TestOrchestrator(testConfig);

      // Execute all enabled test suites
      const testResults = await this.testOrchestrator.executeAll();
      const aggregatedResults = this.testOrchestrator.aggregateResults(testResults);

      const duration = Date.now() - startTime;
      const success = aggregatedResults.overallPassed;

      // Collect errors and warnings from test failures
      const errors: string[] = [];
      const warnings: string[] = [];

      Object.entries(aggregatedResults.failuresByType).forEach(([suiteType, failures]) => {
        failures.forEach(failure => {
          errors.push(`${suiteType}: ${failure.testName} - ${failure.error}`);
        });
      });

      // Add warnings for test suites that had issues but didn't fail completely
      testResults.forEach(result => {
        if (result.passed && result.failures.length > 0) {
          warnings.push(`Some tests in suite had issues but overall suite passed`);
        }
      });

      return {
        success,
        duration,
        data: testResults,
        errors,
        warnings,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        success: false,
        duration,
        data: [],
        errors: [errorMessage],
        warnings: [],
      };
    }
  }
}

class PerformanceMonitoringStage implements VerificationStage {
  name = 'Performance Monitoring';
  private configManager: ConfigManager;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
  }

  async execute(): Promise<VerificationStageResult> {
    const startTime = Date.now();
    
    try {
      const config = this.configManager.getConfig();
      const performanceMonitor = createPerformanceMonitor(config);
      
      // Critical pages to test
      const criticalPages = ['/', '/team-sales', '/harbor-city'];
      const baseUrl = 'http://localhost:8080';
      
      // Launch browser for performance testing
      const browser = await chromium.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--remote-debugging-port=9222'],
      });

      try {
        // Test all critical pages
        const results = await performanceMonitor.testCriticalPages(browser, criticalPages, baseUrl);
        const report = performanceMonitor.generatePerformanceReport(results);

        const duration = Date.now() - startTime;

        // Collect errors and warnings from performance violations
        const errors: string[] = [];
        const warnings: string[] = [];

        results.forEach((result, index) => {
          const pagePath = criticalPages[index];
          result.violations.forEach(violation => {
            const message = `${pagePath}: ${violation.metric} ${violation.actual} exceeds threshold ${violation.threshold}`;
            if (violation.severity === 'error') {
              errors.push(message);
            } else {
              warnings.push(message);
            }
          });
        });

        // Add overall performance warnings
        if (!report.overallSuccess) {
          errors.push(`Performance testing failed with ${report.totalViolations} violations`);
        }

        if (report.averageMetrics.lighthouse.performance < config.performanceThresholds.lighthousePerformance) {
          const message = `Average Lighthouse performance score ${report.averageMetrics.lighthouse.performance} below threshold ${config.performanceThresholds.lighthousePerformance}`;
          if (report.averageMetrics.lighthouse.performance < config.performanceThresholds.lighthousePerformance * 0.8) {
            errors.push(message);
          } else {
            warnings.push(message);
          }
        }

        return {
          success: report.overallSuccess,
          duration,
          data: report.averageMetrics,
          errors,
          warnings,
        };

      } finally {
        await browser.close();
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        success: false,
        duration,
        data: {
          lcp: 0,
          fid: 0,
          cls: 0,
          fcp: 0,
          lighthouse: { performance: 0, accessibility: 0, bestPractices: 0, seo: 0 },
        },
        errors: [`Performance monitoring failed: ${errorMessage}`],
        warnings: [],
      };
    }
  }
}

class AccessibilityValidationStage implements VerificationStage {
  name = 'Accessibility Validation';
  private configManager: ConfigManager;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
  }

  async execute(): Promise<VerificationStageResult> {
    const startTime = Date.now();
    
    try {
      const config = this.configManager.getConfig();
      const baseUrl = 'http://localhost:8080';
      
      // Create accessibility validator
      const validator = createAccessibilityValidator(baseUrl, config);
      
      // Run accessibility validation
      const result = await validator.validate();
      
      const duration = Date.now() - startTime;

      // Collect errors and warnings from accessibility violations
      const errors: string[] = [];
      const warnings: string[] = [];

      result.violations.forEach(violation => {
        const message = `${violation.rule}: ${violation.description} (Element: ${violation.element})`;
        if (violation.impact === 'critical' || violation.impact === 'serious') {
          errors.push(message);
        } else {
          warnings.push(message);
        }
      });

      result.warnings.forEach(warning => {
        warnings.push(`${warning.rule}: ${warning.description} (Element: ${warning.element})`);
      });

      // Add summary information
      if (!result.compliant) {
        const criticalCount = result.violations.filter(v => v.impact === 'critical').length;
        const seriousCount = result.violations.filter(v => v.impact === 'serious').length;
        
        if (criticalCount > 0) {
          errors.push(`Found ${criticalCount} critical accessibility violations`);
        }
        if (seriousCount > 0) {
          errors.push(`Found ${seriousCount} serious accessibility violations`);
        }
      }

      return {
        success: result.compliant,
        duration,
        data: result,
        errors,
        warnings,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        success: false,
        duration,
        data: {
          compliant: false,
          violations: [],
          warnings: [],
          testedPages: [],
        },
        errors: [`Accessibility validation failed: ${errorMessage}`],
        warnings: [],
      };
    }
  }
}

class PWAValidationStage implements VerificationStage {
  name = 'PWA Validation';

  async execute(): Promise<VerificationStageResult> {
    // Placeholder implementation
    return {
      success: true,
      duration: 0,
      data: {
        serviceWorkerRegistered: true,
        manifestValid: true,
        offlineFunctionality: true,
        installable: true,
        cacheStrategy: { staticAssetsCache: true, apiResponseCache: true, offlinePages: [] },
      },
      errors: [],
      warnings: [],
    };
  }
}

class DependencyCheckStage implements VerificationStage {
  name = 'Dependency Check';

  async execute(): Promise<VerificationStageResult> {
    // Placeholder implementation
    return {
      success: true,
      duration: 0,
      data: {
        googleMaps: { service: 'Google Maps', available: true, responseTime: 100 },
        cdnResources: [],
        apiEndpoints: [],
      },
      errors: [],
      warnings: [],
    };
  }
}

/**
 * Factory function to create and execute verification pipeline
 */
export async function runVerification(options: PipelineOptions): Promise<VerificationReport> {
  const pipeline = new VerificationPipeline(options);
  return await pipeline.execute();
}