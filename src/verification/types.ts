/**
 * TypeScript interfaces for the production deployment verification system
 */

// Build Verification Types
export interface BuildError {
  file: string;
  line: number;
  column: number;
  message: string;
  type: 'typescript' | 'bundling' | 'asset';
}

export interface BuildWarning {
  file: string;
  line: number;
  column: number;
  message: string;
  type: 'typescript' | 'bundling' | 'asset';
}

export interface ChunkInfo {
  name: string;
  size: number;
  modules: string[];
}

export interface BuildVerificationResult {
  success: boolean;
  buildTime: number;
  errors: BuildError[];
  warnings: BuildWarning[];
  outputSize: {
    total: number;
    chunks: ChunkInfo[];
  };
}

// Test Types
export interface TestFailure {
  testName: string;
  error: string;
  stack?: string;
  duration: number;
}

export interface CoverageReport {
  lines: number;
  functions: number;
  branches: number;
  statements: number;
}

export interface TestResult {
  passed: boolean;
  duration: number;
  testCount: number;
  failures: TestFailure[];
  coverage?: CoverageReport;
}

export interface TestSuite {
  name: string;
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility';
  execute(): Promise<TestResult>;
}

// Performance Types
export interface LighthouseScore {
  performance: number;
  accessibility: number;
  bestPractices: number;
  seo: number;
}

export interface PerformanceMetrics {
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  lighthouse: LighthouseScore;
}

// Accessibility Types
export interface A11yViolation {
  rule: string;
  impact: 'critical' | 'serious' | 'moderate' | 'minor';
  element: string;
  description: string;
}

export interface A11yWarning {
  rule: string;
  element: string;
  description: string;
}

export interface AccessibilityResult {
  compliant: boolean;
  violations: A11yViolation[];
  warnings: A11yWarning[];
  testedPages: string[];
}

// PWA Types
export interface CacheValidation {
  staticAssetsCache: boolean;
  apiResponseCache: boolean;
  offlinePages: string[];
}

export interface PWAValidationResult {
  serviceWorkerRegistered: boolean;
  manifestValid: boolean;
  offlineFunctionality: boolean;
  installable: boolean;
  cacheStrategy: CacheValidation;
}

// External Dependencies Types
export interface DependencyStatus {
  service: string;
  available: boolean;
  responseTime: number;
  error?: string;
}

export interface ExternalDependencies {
  googleMaps: DependencyStatus;
  cdnResources: DependencyStatus[];
  apiEndpoints: DependencyStatus[];
}

// Configuration Types
export interface TestSuiteConfig {
  name: string;
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility';
  enabled: boolean;
  timeout: number;
  retries: number;
}

export interface ExternalDependency {
  name: string;
  url: string;
  timeout: number;
  critical: boolean;
}

export interface VerificationConfig {
  buildSettings: {
    mode: 'production' | 'development';
    sourceMaps: boolean;
    minification: boolean;
  };
  performanceThresholds: {
    lcp: number; // 2500ms
    fid: number; // 100ms
    cls: number; // 0.1
    lighthousePerformance: number; // 90
  };
  accessibilityLevel: 'AA' | 'AAA';
  testSuites: TestSuiteConfig[];
  externalDependencies: ExternalDependency[];
}

// Report Types
export interface VerificationReport {
  timestamp: Date;
  overallStatus: 'passed' | 'failed' | 'warning';
  buildVerification: BuildVerificationResult;
  testResults: TestResult[];
  performanceMetrics: PerformanceMetrics;
  accessibilityResults: AccessibilityResult;
  pwaValidation: PWAValidationResult;
  dependencyStatus: ExternalDependencies;
  recommendations: string[];
  deploymentReady: boolean;
}

// Pipeline Types
export interface VerificationStage {
  name: string;
  execute(): Promise<VerificationStageResult>;
}

export interface VerificationStageResult {
  success: boolean;
  duration: number;
  data: any;
  errors: string[];
  warnings: string[];
}

export interface PipelineOptions {
  config: VerificationConfig;
  skipStages?: string[];
  verbose?: boolean;
  outputFormat?: 'json' | 'html' | 'console';
}