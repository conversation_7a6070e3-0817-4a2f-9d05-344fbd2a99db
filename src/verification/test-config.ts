/**
 * Test Configuration Management System
 * 
 * Manages test suite configurations and provides defaults
 */

import { promises as fs } from 'fs';
import path from 'path';
import { TestSuiteConfig } from './types.js';
import { TestOrchestratorConfig } from './test-orchestrator.js';

export interface TestConfigOptions {
  configPath?: string;
  environment?: 'development' | 'production' | 'test';
  overrides?: Partial<TestOrchestratorConfig>;
}

export class TestConfigManager {
  private static readonly DEFAULT_CONFIG: TestOrchestratorConfig = {
    testSuites: [
      {
        name: 'unit',
        type: 'unit',
        enabled: true,
        timeout: 30000,
        retries: 0
      },
      {
        name: 'integration',
        type: 'integration',
        enabled: true,
        timeout: 45000,
        retries: 1
      },
      {
        name: 'e2e',
        type: 'e2e',
        enabled: true,
        timeout: 60000,
        retries: 2
      }
    ],
    jestConfig: 'jest.config.js',
    playwrightConfig: 'playwright.config.ts',
    outputDir: './test-results',
    parallel: false,
    timeout: 300000 // 5 minutes overall timeout
  };

  private static readonly ENVIRONMENT_OVERRIDES: Record<string, Partial<TestOrchestratorConfig>> = {
    development: {
      parallel: false,
      timeout: 600000, // 10 minutes for dev
      testSuites: [
        {
          name: 'unit',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 0
        },
        {
          name: 'integration',
          type: 'integration',
          enabled: false, // Skip integration in dev by default
          timeout: 45000,
          retries: 1
        },
        {
          name: 'e2e',
          type: 'e2e',
          enabled: false, // Skip E2E in dev by default
          timeout: 60000,
          retries: 2
        }
      ]
    },
    production: {
      parallel: true,
      timeout: 300000, // 5 minutes for production
      testSuites: [
        {
          name: 'unit',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 1
        },
        {
          name: 'integration',
          type: 'integration',
          enabled: true,
          timeout: 45000,
          retries: 2
        },
        {
          name: 'e2e',
          type: 'e2e',
          enabled: true,
          timeout: 60000,
          retries: 3
        }
      ]
    },
    test: {
      parallel: true,
      timeout: 180000, // 3 minutes for test env
      testSuites: [
        {
          name: 'unit',
          type: 'unit',
          enabled: true,
          timeout: 20000,
          retries: 0
        },
        {
          name: 'integration',
          type: 'integration',
          enabled: true,
          timeout: 30000,
          retries: 1
        },
        {
          name: 'e2e',
          type: 'e2e',
          enabled: true,
          timeout: 45000,
          retries: 1
        }
      ]
    }
  };

  /**
   * Load test configuration from file or use defaults
   */
  static async loadConfig(options: TestConfigOptions = {}): Promise<TestOrchestratorConfig> {
    let config = { ...this.DEFAULT_CONFIG };

    // Apply environment-specific overrides
    if (options.environment && this.ENVIRONMENT_OVERRIDES[options.environment]) {
      config = this.mergeConfigs(config, this.ENVIRONMENT_OVERRIDES[options.environment]);
    }

    // Load from config file if specified
    if (options.configPath) {
      try {
        const fileConfig = await this.loadConfigFromFile(options.configPath);
        config = this.mergeConfigs(config, fileConfig);
      } catch (error) {
        console.warn(`Failed to load config from ${options.configPath}:`, error);
      }
    }

    // Apply user overrides
    if (options.overrides) {
      config = this.mergeConfigs(config, options.overrides);
    }

    return this.validateConfig(config);
  }

  /**
   * Save configuration to file
   */
  static async saveConfig(config: TestOrchestratorConfig, filePath: string): Promise<void> {
    const configJson = JSON.stringify(config, null, 2);
    await fs.writeFile(filePath, configJson, 'utf8');
  }

  /**
   * Create a default configuration file
   */
  static async createDefaultConfig(filePath: string): Promise<void> {
    await this.saveConfig(this.DEFAULT_CONFIG, filePath);
  }

  /**
   * Get configuration for a specific test suite
   */
  static getTestSuiteConfig(config: TestOrchestratorConfig, suiteName: string): TestSuiteConfig | undefined {
    return config.testSuites.find(suite => suite.name === suiteName);
  }

  /**
   * Update configuration for a specific test suite
   */
  static updateTestSuiteConfig(
    config: TestOrchestratorConfig, 
    suiteName: string, 
    updates: Partial<TestSuiteConfig>
  ): TestOrchestratorConfig {
    const updatedConfig = { 
      ...config,
      testSuites: [...config.testSuites] // Create a shallow copy of the array
    };
    const suiteIndex = updatedConfig.testSuites.findIndex(suite => suite.name === suiteName);
    
    if (suiteIndex !== -1) {
      updatedConfig.testSuites[suiteIndex] = {
        ...updatedConfig.testSuites[suiteIndex],
        ...updates
      };
    }

    return updatedConfig;
  }

  /**
   * Enable or disable a test suite
   */
  static toggleTestSuite(config: TestOrchestratorConfig, suiteName: string, enabled: boolean): TestOrchestratorConfig {
    return this.updateTestSuiteConfig(config, suiteName, { enabled });
  }

  /**
   * Get enabled test suites
   */
  static getEnabledTestSuites(config: TestOrchestratorConfig): TestSuiteConfig[] {
    return config.testSuites.filter(suite => suite.enabled);
  }

  /**
   * Validate configuration
   */
  private static validateConfig(config: TestOrchestratorConfig): TestOrchestratorConfig {
    // Validate test suites
    config.testSuites.forEach(suite => {
      if (!suite.name) {
        throw new Error('Test suite name is required');
      }
      if (!['unit', 'integration', 'e2e', 'performance', 'accessibility'].includes(suite.type)) {
        throw new Error(`Invalid test suite type: ${suite.type}`);
      }
      if (suite.timeout <= 0) {
        throw new Error(`Invalid timeout for suite ${suite.name}: ${suite.timeout}`);
      }
      if (suite.retries < 0) {
        throw new Error(`Invalid retries for suite ${suite.name}: ${suite.retries}`);
      }
    });

    // Validate overall timeout
    if (config.timeout && config.timeout <= 0) {
      throw new Error(`Invalid overall timeout: ${config.timeout}`);
    }

    // Validate output directory
    if (config.outputDir && !path.isAbsolute(config.outputDir) && !config.outputDir.startsWith('./')) {
      config.outputDir = `./${config.outputDir}`;
    }

    return config;
  }

  /**
   * Load configuration from JSON file
   */
  private static async loadConfigFromFile(filePath: string): Promise<Partial<TestOrchestratorConfig>> {
    const configContent = await fs.readFile(filePath, 'utf8');
    return JSON.parse(configContent);
  }

  /**
   * Deep merge two configuration objects
   */
  private static mergeConfigs(
    base: TestOrchestratorConfig, 
    override: Partial<TestOrchestratorConfig>
  ): TestOrchestratorConfig {
    const merged = { ...base };

    // Merge simple properties
    Object.keys(override).forEach(key => {
      if (key !== 'testSuites' && override[key as keyof TestOrchestratorConfig] !== undefined) {
        (merged as any)[key] = override[key as keyof TestOrchestratorConfig];
      }
    });

    // Merge test suites array
    if (override.testSuites) {
      merged.testSuites = override.testSuites.map(overrideSuite => {
        const baseSuite = base.testSuites.find(suite => suite.name === overrideSuite.name);
        return baseSuite ? { ...baseSuite, ...overrideSuite } : overrideSuite;
      });

      // Add any base suites not in override
      base.testSuites.forEach(baseSuite => {
        if (!override.testSuites!.find(suite => suite.name === baseSuite.name)) {
          merged.testSuites.push(baseSuite);
        }
      });
    }

    return merged;
  }

  /**
   * Get configuration schema for validation
   */
  static getConfigSchema(): object {
    return {
      type: 'object',
      properties: {
        testSuites: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              type: { 
                type: 'string', 
                enum: ['unit', 'integration', 'e2e', 'performance', 'accessibility'] 
              },
              enabled: { type: 'boolean' },
              timeout: { type: 'number', minimum: 1 },
              retries: { type: 'number', minimum: 0 }
            },
            required: ['name', 'type', 'enabled', 'timeout', 'retries']
          }
        },
        jestConfig: { type: 'string' },
        playwrightConfig: { type: 'string' },
        outputDir: { type: 'string' },
        parallel: { type: 'boolean' },
        timeout: { type: 'number', minimum: 1 }
      },
      required: ['testSuites']
    };
  }
}