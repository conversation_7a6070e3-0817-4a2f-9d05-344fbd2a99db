/**
 * Performance monitoring system using Playwright and Lighthouse
 * Measures Core Web Vitals and validates against thresholds
 */

import { <PERSON>, Browser } from '@playwright/test';
import { playAudit } from 'playwright-lighthouse';
import { PerformanceMetrics, LighthouseScore, VerificationConfig } from './types';

export interface PerformanceTestOptions {
  url: string;
  thresholds: {
    lcp: number;
    fid: number;
    cls: number;
    lighthousePerformance: number;
  };
  timeout?: number;
  retries?: number;
}

export interface CoreWebVitals {
  lcp: number;
  fid: number;
  cls: number;
  fcp: number;
}

export interface PerformanceTestResult {
  success: boolean;
  metrics: PerformanceMetrics;
  violations: PerformanceViolation[];
  duration: number;
  url: string;
}

export interface PerformanceViolation {
  metric: string;
  actual: number;
  threshold: number;
  severity: 'warning' | 'error';
}

/**
 * Performance monitoring class that integrates Playwright with Lighthouse
 */
export class PerformanceMonitor {
  private config: VerificationConfig;

  constructor(config: VerificationConfig) {
    this.config = config;
  }

  /**
   * Run comprehensive performance tests on a page
   */
  async runPerformanceTest(
    page: Page,
    url: string,
    options?: Partial<PerformanceTestOptions>
  ): Promise<PerformanceTestResult> {
    const startTime = Date.now();
    const testOptions: PerformanceTestOptions = {
      url,
      thresholds: this.config.performanceThresholds,
      timeout: 180000,
      retries: 2,
      ...options,
    };

    try {
      // Navigate to the page
      await page.goto(url, { waitUntil: 'networkidle' });

      // Collect Core Web Vitals
      const coreWebVitals = await this.collectCoreWebVitals(page);

      // Run Lighthouse audit
      const lighthouseScore = await this.runLighthouseAudit(page, url);

      // Combine metrics
      const metrics: PerformanceMetrics = {
        ...coreWebVitals,
        lighthouse: lighthouseScore,
      };

      // Validate against thresholds
      const violations = this.validateThresholds(metrics, testOptions.thresholds);

      const duration = Date.now() - startTime;

      return {
        success: violations.filter(v => v.severity === 'error').length === 0,
        metrics,
        violations,
        duration,
        url,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      throw new Error(`Performance test failed for ${url}: ${error}`);
    }
  }

  /**
   * Collect Core Web Vitals using Playwright
   */
  private async collectCoreWebVitals(page: Page): Promise<CoreWebVitals> {
    // Inject web vitals measurement script
    await page.addInitScript(() => {
      // Store web vitals data
      (window as any).webVitals = {};
      
      // Import web-vitals library dynamically
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/web-vitals@3/dist/web-vitals.iife.js';
      script.onload = () => {
        const { getCLS, getFID, getFCP, getLCP } = (window as any).webVitals;
        
        getCLS((metric: any) => {
          (window as any).webVitals.cls = metric.value;
        });
        
        getFID((metric: any) => {
          (window as any).webVitals.fid = metric.value;
        });
        
        getFCP((metric: any) => {
          (window as any).webVitals.fcp = metric.value;
        });
        
        getLCP((metric: any) => {
          (window as any).webVitals.lcp = metric.value;
        });
      };
      document.head.appendChild(script);
    });

    // Wait for page to load and metrics to be collected
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Allow time for metrics collection

    // Extract metrics from the page
    const webVitals = await page.evaluate(() => {
      return (window as any).webVitals || {};
    });

    // Fallback to performance API if web-vitals library fails
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      const fcp = paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0;
      
      return {
        fcp,
        navigationStart: navigation?.startTime || 0,
        loadComplete: navigation?.loadEventEnd || 0,
      };
    });

    return {
      lcp: webVitals.lcp || performanceMetrics.loadComplete || 0,
      fid: webVitals.fid || 0,
      cls: webVitals.cls || 0,
      fcp: webVitals.fcp || performanceMetrics.fcp || 0,
    };
  }

  /**
   * Run Lighthouse audit using playwright-lighthouse
   */
  private async runLighthouseAudit(page: Page, url: string): Promise<LighthouseScore> {
    try {
      // Configure Lighthouse options
      const lighthouseOptions = {
        port: 9222, // Chrome debugging port
        disableStorageReset: false,
        formFactor: 'desktop' as const,
        screenEmulation: {
          mobile: false,
          width: 1350,
          height: 940,
          deviceScaleFactor: 1,
          disabled: false,
        },
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0,
        },
      };

      // Run Lighthouse audit
      const result = await playAudit({
        page,
        url,
        port: 9222,
        options: lighthouseOptions,
      });

      if (!result || !result.lhr) {
        throw new Error('Lighthouse audit failed to return results');
      }

      const { lhr } = result;

      return {
        performance: Math.round((lhr.categories.performance?.score || 0) * 100),
        accessibility: Math.round((lhr.categories.accessibility?.score || 0) * 100),
        bestPractices: Math.round((lhr.categories['best-practices']?.score || 0) * 100),
        seo: Math.round((lhr.categories.seo?.score || 0) * 100),
      };
    } catch (error) {
      console.warn('Lighthouse audit failed, using fallback scores:', error);
      
      // Return fallback scores if Lighthouse fails
      return {
        performance: 0,
        accessibility: 0,
        bestPractices: 0,
        seo: 0,
      };
    }
  }

  /**
   * Validate metrics against configured thresholds
   */
  private validateThresholds(
    metrics: PerformanceMetrics,
    thresholds: PerformanceTestOptions['thresholds']
  ): PerformanceViolation[] {
    const violations: PerformanceViolation[] = [];

    // Check LCP (Largest Contentful Paint)
    if (metrics.lcp > thresholds.lcp) {
      violations.push({
        metric: 'LCP',
        actual: metrics.lcp,
        threshold: thresholds.lcp,
        severity: metrics.lcp > thresholds.lcp * 1.5 ? 'error' : 'warning',
      });
    }

    // Check FID (First Input Delay)
    if (metrics.fid > thresholds.fid) {
      violations.push({
        metric: 'FID',
        actual: metrics.fid,
        threshold: thresholds.fid,
        severity: metrics.fid > thresholds.fid * 2 ? 'error' : 'warning',
      });
    }

    // Check CLS (Cumulative Layout Shift)
    if (metrics.cls > thresholds.cls) {
      violations.push({
        metric: 'CLS',
        actual: metrics.cls,
        threshold: thresholds.cls,
        severity: metrics.cls > thresholds.cls * 2 ? 'error' : 'warning',
      });
    }

    // Check Lighthouse Performance Score
    if (metrics.lighthouse.performance < thresholds.lighthousePerformance) {
      violations.push({
        metric: 'Lighthouse Performance',
        actual: metrics.lighthouse.performance,
        threshold: thresholds.lighthousePerformance,
        severity: metrics.lighthouse.performance < thresholds.lighthousePerformance * 0.8 ? 'error' : 'warning',
      });
    }

    return violations;
  }

  /**
   * Test multiple critical pages for performance
   */
  async testCriticalPages(
    browser: Browser,
    pages: string[],
    baseUrl: string = 'http://localhost:8080'
  ): Promise<PerformanceTestResult[]> {
    const results: PerformanceTestResult[] = [];

    for (const pagePath of pages) {
      const page = await browser.newPage();
      const url = `${baseUrl}${pagePath}`;

      try {
        const result = await this.runPerformanceTest(page, url);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          metrics: {
            lcp: 0,
            fid: 0,
            cls: 0,
            fcp: 0,
            lighthouse: { performance: 0, accessibility: 0, bestPractices: 0, seo: 0 },
          },
          violations: [{
            metric: 'Test Execution',
            actual: 0,
            threshold: 1,
            severity: 'error',
          }],
          duration: 0,
          url,
        });
      } finally {
        await page.close();
      }
    }

    return results;
  }

  /**
   * Generate performance report summary
   */
  generatePerformanceReport(results: PerformanceTestResult[]): {
    overallSuccess: boolean;
    totalViolations: number;
    averageMetrics: PerformanceMetrics;
    pageResults: PerformanceTestResult[];
  } {
    const totalViolations = results.reduce((sum, result) => sum + result.violations.length, 0);
    const overallSuccess = results.every(result => result.success);

    // Calculate average metrics
    const avgMetrics = results.reduce(
      (acc, result) => ({
        lcp: acc.lcp + result.metrics.lcp,
        fid: acc.fid + result.metrics.fid,
        cls: acc.cls + result.metrics.cls,
        fcp: acc.fcp + result.metrics.fcp,
        lighthouse: {
          performance: acc.lighthouse.performance + result.metrics.lighthouse.performance,
          accessibility: acc.lighthouse.accessibility + result.metrics.lighthouse.accessibility,
          bestPractices: acc.lighthouse.bestPractices + result.metrics.lighthouse.bestPractices,
          seo: acc.lighthouse.seo + result.metrics.lighthouse.seo,
        },
      }),
      {
        lcp: 0,
        fid: 0,
        cls: 0,
        fcp: 0,
        lighthouse: { performance: 0, accessibility: 0, bestPractices: 0, seo: 0 },
      }
    );

    const count = results.length || 1;
    const averageMetrics: PerformanceMetrics = {
      lcp: Math.round(avgMetrics.lcp / count),
      fid: Math.round(avgMetrics.fid / count),
      cls: Math.round((avgMetrics.cls / count) * 1000) / 1000, // Round to 3 decimal places
      fcp: Math.round(avgMetrics.fcp / count),
      lighthouse: {
        performance: Math.round(avgMetrics.lighthouse.performance / count),
        accessibility: Math.round(avgMetrics.lighthouse.accessibility / count),
        bestPractices: Math.round(avgMetrics.lighthouse.bestPractices / count),
        seo: Math.round(avgMetrics.lighthouse.seo / count),
      },
    };

    return {
      overallSuccess,
      totalViolations,
      averageMetrics,
      pageResults: results,
    };
  }
}

/**
 * Create a performance monitor instance
 */
export const createPerformanceMonitor = (config: VerificationConfig): PerformanceMonitor => {
  return new PerformanceMonitor(config);
};