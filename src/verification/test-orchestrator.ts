/**
 * Test Orchestrator Framework
 * 
 * Manages and executes Jest unit tests and Playwright E2E tests
 * Provides test result aggregation and failure reporting
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { TestSuite, TestResult, TestFailure, CoverageReport, TestSuiteConfig } from './types.js';
import { AccessibilityTestSuite } from './accessibility';
import { ConfigManager } from './config';

export interface TestOrchestratorConfig {
  testSuites: TestSuiteConfig[];
  jestConfig?: string;
  playwrightConfig?: string;
  outputDir?: string;
  parallel?: boolean;
  timeout?: number;
}

export interface TestExecutionOptions {
  verbose?: boolean;
  coverage?: boolean;
  bail?: boolean;
  pattern?: string;
}

export class TestOrchestrator {
  private config: TestOrchestratorConfig;
  private testSuites: Map<string, TestSuite> = new Map();
  private configManager?: ConfigManager;

  constructor(config: TestOrchestratorConfig, configManager?: ConfigManager) {
    this.config = config;
    this.configManager = configManager;
    this.initializeTestSuites();
  }

  private initializeTestSuites(): void {
    // Initialize Jest unit test suite
    this.testSuites.set('unit', new JestTestSuite({
      name: 'unit',
      type: 'unit',
      configPath: this.config.jestConfig,
      timeout: this.config.timeout || 30000
    }));

    // Initialize Playwright E2E test suite
    this.testSuites.set('e2e', new PlaywrightTestSuite({
      name: 'e2e',
      type: 'e2e',
      configPath: this.config.playwrightConfig,
      timeout: this.config.timeout || 60000
    }));

    // Initialize integration test suite (Jest with different pattern)
    this.testSuites.set('integration', new JestTestSuite({
      name: 'integration',
      type: 'integration',
      configPath: this.config.jestConfig,
      testPattern: '**/*.integration.test.{js,ts,tsx}',
      timeout: this.config.timeout || 45000
    }));

    // Initialize accessibility test suite if config manager is available
    if (this.configManager) {
      this.testSuites.set('accessibility', new AccessibilityTestSuite(this.configManager));
    }
  }

  /**
   * Execute all enabled test suites
   */
  async executeAll(options: TestExecutionOptions = {}): Promise<TestResult[]> {
    const results: TestResult[] = [];
    const enabledSuites = this.config.testSuites.filter(suite => suite.enabled);

    if (this.config.parallel) {
      // Execute test suites in parallel
      const promises = enabledSuites.map(async (suiteConfig) => {
        const suite = this.testSuites.get(suiteConfig.name);
        if (!suite) {
          throw new Error(`Test suite '${suiteConfig.name}' not found`);
        }
        return this.executeSuite(suite, options);
      });

      const parallelResults = await Promise.allSettled(promises);
      
      for (const result of parallelResults) {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          // Handle failed test suite execution
          results.push({
            passed: false,
            duration: 0,
            testCount: 0,
            failures: [{
              testName: 'Test Suite Execution',
              error: result.reason.message || 'Unknown error',
              duration: 0
            }]
          });
        }
      }
    } else {
      // Execute test suites sequentially
      for (const suiteConfig of enabledSuites) {
        const suite = this.testSuites.get(suiteConfig.name);
        if (!suite) {
          throw new Error(`Test suite '${suiteConfig.name}' not found`);
        }

        try {
          const result = await this.executeSuite(suite, options);
          results.push(result);

          // Bail on first failure if requested
          if (options.bail && !result.passed) {
            break;
          }
        } catch (error) {
          results.push({
            passed: false,
            duration: 0,
            testCount: 0,
            failures: [{
              testName: `${suite.name} Suite Execution`,
              error: error instanceof Error ? error.message : 'Unknown error',
              duration: 0
            }]
          });

          if (options.bail) {
            break;
          }
        }
      }
    }

    return results;
  }

  /**
   * Execute a specific test suite
   */
  async executeSuite(suite: TestSuite, options: TestExecutionOptions = {}): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const result = await suite.execute();
      const duration = Date.now() - startTime;
      
      return {
        ...result,
        duration: duration
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      return {
        passed: false,
        duration: duration,
        testCount: 0,
        failures: [{
          testName: `${suite.name} execution`,
          error: error instanceof Error ? error.message : 'Unknown error',
          duration: duration
        }]
      };
    }
  }

  /**
   * Get aggregated test results summary
   */
  aggregateResults(results: TestResult[]): {
    totalTests: number;
    totalPassed: number;
    totalFailed: number;
    totalDuration: number;
    overallPassed: boolean;
    failuresByType: Record<string, TestFailure[]>;
  } {
    const totalTests = results.reduce((sum, result) => sum + result.testCount, 0);
    const totalPassed = results.reduce((sum, result) => 
      sum + (result.testCount - result.failures.length), 0);
    const totalFailed = results.reduce((sum, result) => sum + result.failures.length, 0);
    const totalDuration = results.reduce((sum, result) => sum + result.duration, 0);
    const overallPassed = results.every(result => result.passed);

    // Group failures by test suite type
    const failuresByType: Record<string, TestFailure[]> = {};
    results.forEach((result, index) => {
      const suiteConfig = this.config.testSuites[index];
      if (suiteConfig && result.failures.length > 0) {
        failuresByType[suiteConfig.type] = result.failures;
      }
    });

    return {
      totalTests,
      totalPassed,
      totalFailed,
      totalDuration,
      overallPassed,
      failuresByType
    };
  }

  /**
   * Generate detailed test report
   */
  async generateReport(results: TestResult[], outputPath?: string): Promise<string> {
    const aggregated = this.aggregateResults(results);
    const timestamp = new Date().toISOString();

    const report = {
      timestamp,
      summary: aggregated,
      results: results.map((result, index) => ({
        suite: this.config.testSuites[index]?.name || `Suite ${index}`,
        type: this.config.testSuites[index]?.type || 'unknown',
        ...result
      }))
    };

    const reportJson = JSON.stringify(report, null, 2);

    if (outputPath) {
      await fs.writeFile(outputPath, reportJson);
    }

    return reportJson;
  }
}

/**
 * Jest Test Suite Implementation
 */
class JestTestSuite implements TestSuite {
  name: string;
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility';
  private configPath?: string;
  private testPattern?: string;
  private timeout: number;

  constructor(options: {
    name: string;
    type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility';
    configPath?: string;
    testPattern?: string;
    timeout: number;
  }) {
    this.name = options.name;
    this.type = options.type;
    this.configPath = options.configPath;
    this.testPattern = options.testPattern;
    this.timeout = options.timeout;
  }

  async execute(): Promise<TestResult> {
    return new Promise((resolve, reject) => {
      const args = ['--json', '--passWithNoTests'];
      
      if (this.configPath) {
        args.push('--config', this.configPath);
      }
      
      if (this.testPattern) {
        args.push('--testPathPattern', this.testPattern);
      }

      const jestProcess = spawn('npx', ['jest', ...args], {
        stdio: ['pipe', 'pipe', 'pipe'],
        timeout: this.timeout
      });

      let stdout = '';
      let stderr = '';

      jestProcess.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      jestProcess.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      jestProcess.on('close', (code) => {
        try {
          const result = this.parseJestOutput(stdout, stderr, code === 0);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      jestProcess.on('error', (error) => {
        reject(error);
      });
    });
  }

  private parseJestOutput(stdout: string, stderr: string, success: boolean): TestResult {
    try {
      // Try to parse Jest JSON output
      const lines = stdout.split('\n');
      const jsonLine = lines.find(line => line.trim().startsWith('{'));
      
      if (jsonLine) {
        const jestResult = JSON.parse(jsonLine);
        
        const failures: TestFailure[] = [];
        
        if (jestResult.testResults) {
          jestResult.testResults.forEach((testFile: any) => {
            testFile.assertionResults?.forEach((assertion: any) => {
              if (assertion.status === 'failed') {
                failures.push({
                  testName: `${testFile.name}: ${assertion.title}`,
                  error: assertion.failureMessages?.join('\n') || 'Test failed',
                  stack: assertion.failureMessages?.join('\n'),
                  duration: assertion.duration || 0
                });
              }
            });
          });
        }

        return {
          passed: success && failures.length === 0,
          duration: jestResult.runTime || 0,
          testCount: jestResult.numTotalTests || 0,
          failures,
          coverage: this.parseCoverage(jestResult.coverageMap)
        };
      }
    } catch (error) {
      // Fallback parsing if JSON parsing fails
    }

    // Fallback result if parsing fails
    return {
      passed: success,
      duration: 0,
      testCount: 0,
      failures: success ? [] : [{
        testName: 'Jest execution',
        error: stderr || 'Jest execution failed',
        duration: 0
      }]
    };
  }

  private parseCoverage(coverageMap: any): CoverageReport | undefined {
    if (!coverageMap) return undefined;

    // Simple coverage parsing - in a real implementation,
    // you'd want more sophisticated coverage analysis
    return {
      lines: 0,
      functions: 0,
      branches: 0,
      statements: 0
    };
  }
}

/**
 * Playwright Test Suite Implementation
 */
class PlaywrightTestSuite implements TestSuite {
  name: string;
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility';
  private configPath?: string;
  private timeout: number;

  constructor(options: {
    name: string;
    type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility';
    configPath?: string;
    timeout: number;
  }) {
    this.name = options.name;
    this.type = options.type;
    this.configPath = options.configPath;
    this.timeout = options.timeout;
  }

  async execute(): Promise<TestResult> {
    return new Promise((resolve, reject) => {
      const args = ['--reporter=json'];
      
      if (this.configPath) {
        args.push('--config', this.configPath);
      }

      const playwrightProcess = spawn('npx', ['playwright', 'test', ...args], {
        stdio: ['pipe', 'pipe', 'pipe'],
        timeout: this.timeout
      });

      let stdout = '';
      let stderr = '';

      playwrightProcess.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      playwrightProcess.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      playwrightProcess.on('close', (code) => {
        try {
          const result = this.parsePlaywrightOutput(stdout, stderr, code === 0);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      playwrightProcess.on('error', (error) => {
        reject(error);
      });
    });
  }

  private parsePlaywrightOutput(stdout: string, stderr: string, success: boolean): TestResult {
    try {
      // Try to parse Playwright JSON output
      const jsonMatch = stdout.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        const playwrightResult = JSON.parse(jsonMatch[0]);
        
        const failures: TestFailure[] = [];
        let testCount = 0;

        if (playwrightResult.suites) {
          this.extractTestResults(playwrightResult.suites, failures, (count) => {
            testCount += count;
          });
        }

        return {
          passed: success && failures.length === 0,
          duration: playwrightResult.stats?.duration || 0,
          testCount,
          failures
        };
      }
    } catch (error) {
      // Fallback parsing if JSON parsing fails
    }

    // Fallback result if parsing fails
    return {
      passed: success,
      duration: 0,
      testCount: 0,
      failures: success ? [] : [{
        testName: 'Playwright execution',
        error: stderr || 'Playwright execution failed',
        duration: 0
      }]
    };
  }

  private extractTestResults(
    suites: any[], 
    failures: TestFailure[], 
    countCallback: (count: number) => void
  ): void {
    suites.forEach(suite => {
      if (suite.tests) {
        suite.tests.forEach((test: any) => {
          countCallback(1);
          
          if (test.results) {
            test.results.forEach((result: any) => {
              if (result.status === 'failed') {
                failures.push({
                  testName: `${suite.title}: ${test.title}`,
                  error: result.error?.message || 'Test failed',
                  stack: result.error?.stack,
                  duration: result.duration || 0
                });
              }
            });
          }
        });
      }

      if (suite.suites) {
        this.extractTestResults(suite.suites, failures, countCallback);
      }
    });
  }
}