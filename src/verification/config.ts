/**
 * Configuration system for the verification pipeline
 */

import { VerificationConfig, TestSuiteConfig, ExternalDependency } from './types';

// Default configuration values
export const DEFAULT_CONFIG: VerificationConfig = {
  buildSettings: {
    mode: 'production',
    sourceMaps: false,
    minification: true,
  },
  performanceThresholds: {
    lcp: 2500, // 2.5 seconds
    fid: 100,  // 100 milliseconds
    cls: 0.1,  // 0.1 cumulative layout shift
    lighthousePerformance: 90, // 90/100 score
  },
  accessibilityLevel: 'AA',
  testSuites: [
    {
      name: 'unit-tests',
      type: 'unit',
      enabled: true,
      timeout: 30000,
      retries: 2,
    },
    {
      name: 'integration-tests',
      type: 'integration',
      enabled: true,
      timeout: 60000,
      retries: 2,
    },
    {
      name: 'e2e-tests',
      type: 'e2e',
      enabled: true,
      timeout: 120000,
      retries: 3,
    },
    {
      name: 'performance-tests',
      type: 'performance',
      enabled: true,
      timeout: 180000,
      retries: 2,
    },
    {
      name: 'accessibility-tests',
      type: 'accessibility',
      enabled: true,
      timeout: 90000,
      retries: 2,
    },
  ],
  externalDependencies: [
    {
      name: 'Google Maps API',
      url: 'https://maps.googleapis.com/maps/api/js',
      timeout: 10000,
      critical: true,
    },
    {
      name: 'CDN Resources',
      url: 'https://fonts.googleapis.com',
      timeout: 5000,
      critical: false,
    },
  ],
};

/**
 * Configuration manager class
 */
export class ConfigManager {
  private config: VerificationConfig;

  constructor(customConfig?: Partial<VerificationConfig>) {
    this.config = this.mergeConfig(DEFAULT_CONFIG, customConfig || {});
  }

  /**
   * Get the current configuration
   */
  getConfig(): VerificationConfig {
    return { ...this.config };
  }

  /**
   * Update configuration with new values
   */
  updateConfig(updates: Partial<VerificationConfig>): void {
    this.config = this.mergeConfig(this.config, updates);
  }

  /**
   * Get performance thresholds
   */
  getPerformanceThresholds() {
    return this.config.performanceThresholds;
  }

  /**
   * Get enabled test suites
   */
  getEnabledTestSuites(): TestSuiteConfig[] {
    return this.config.testSuites.filter(suite => suite.enabled);
  }

  /**
   * Get critical external dependencies
   */
  getCriticalDependencies(): ExternalDependency[] {
    return this.config.externalDependencies.filter(dep => dep.critical);
  }

  /**
   * Get build settings
   */
  getBuildSettings() {
    return this.config.buildSettings;
  }

  /**
   * Validate configuration
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate performance thresholds
    const { lcp, fid, cls, lighthousePerformance } = this.config.performanceThresholds;
    
    if (lcp <= 0) errors.push('LCP threshold must be positive');
    if (fid <= 0) errors.push('FID threshold must be positive');
    if (cls < 0 || cls > 1) errors.push('CLS threshold must be between 0 and 1');
    if (lighthousePerformance < 0 || lighthousePerformance > 100) {
      errors.push('Lighthouse performance threshold must be between 0 and 100');
    }

    // Validate test suites
    if (this.config.testSuites.length === 0) {
      errors.push('At least one test suite must be configured');
    }

    this.config.testSuites.forEach((suite, index) => {
      if (!suite.name) errors.push(`Test suite ${index} must have a name`);
      if (suite.timeout <= 0) errors.push(`Test suite ${suite.name} timeout must be positive`);
      if (suite.retries < 0) errors.push(`Test suite ${suite.name} retries must be non-negative`);
    });

    // Validate external dependencies
    this.config.externalDependencies.forEach((dep, index) => {
      if (!dep.name) errors.push(`External dependency ${index} must have a name`);
      if (!dep.url) errors.push(`External dependency ${dep.name} must have a URL`);
      if (dep.timeout <= 0) errors.push(`External dependency ${dep.name} timeout must be positive`);
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Load configuration from file
   */
  static async loadFromFile(filePath: string): Promise<ConfigManager> {
    try {
      const fs = await import('fs/promises');
      const configData = await fs.readFile(filePath, 'utf-8');
      const customConfig = JSON.parse(configData);
      return new ConfigManager(customConfig);
    } catch (error) {
      console.warn(`Failed to load config from ${filePath}, using defaults:`, error);
      return new ConfigManager();
    }
  }

  /**
   * Save configuration to file
   */
  async saveToFile(filePath: string): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      // Ensure directory exists
      const dir = path.dirname(filePath);
      await fs.mkdir(dir, { recursive: true });
      
      // Write config file
      await fs.writeFile(filePath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      throw new Error(`Failed to save config to ${filePath}: ${error}`);
    }
  }

  /**
   * Merge configurations with deep merge for nested objects
   */
  private mergeConfig(base: VerificationConfig, override: Partial<VerificationConfig>): VerificationConfig {
    const merged = { ...base };

    if (override.buildSettings) {
      merged.buildSettings = { ...base.buildSettings, ...override.buildSettings };
    }

    if (override.performanceThresholds) {
      merged.performanceThresholds = { ...base.performanceThresholds, ...override.performanceThresholds };
    }

    if (override.accessibilityLevel) {
      merged.accessibilityLevel = override.accessibilityLevel;
    }

    if (override.testSuites) {
      merged.testSuites = override.testSuites;
    }

    if (override.externalDependencies) {
      merged.externalDependencies = override.externalDependencies;
    }

    return merged;
  }
}

/**
 * Create a default configuration manager instance
 */
export const createConfigManager = (customConfig?: Partial<VerificationConfig>): ConfigManager => {
  return new ConfigManager(customConfig);
};