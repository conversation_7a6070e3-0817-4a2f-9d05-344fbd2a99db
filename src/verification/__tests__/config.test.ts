/**
 * Tests for the configuration system
 */

import { ConfigManager, DEFAULT_CONFIG } from '../config';
import { VerificationConfig } from '../types';

describe('ConfigManager', () => {
  describe('constructor', () => {
    it('should create config manager with default configuration', () => {
      const configManager = new ConfigManager();
      const config = configManager.getConfig();
      
      expect(config).toEqual(DEFAULT_CONFIG);
    });

    it('should merge custom configuration with defaults', () => {
      const customConfig: Partial<VerificationConfig> = {
        performanceThresholds: {
          lcp: 3000,
          fid: 150,
          cls: 0.2,
          lighthousePerformance: 85,
        },
      };

      const configManager = new ConfigManager(customConfig);
      const config = configManager.getConfig();

      expect(config.performanceThresholds.lcp).toBe(3000);
      expect(config.performanceThresholds.fid).toBe(150);
      expect(config.buildSettings).toEqual(DEFAULT_CONFIG.buildSettings);
    });
  });

  describe('updateConfig', () => {
    it('should update configuration with new values', () => {
      const configManager = new ConfigManager();
      
      configManager.updateConfig({
        accessibilityLevel: 'AAA',
      });

      const config = configManager.getConfig();
      expect(config.accessibilityLevel).toBe('AAA');
    });
  });

  describe('getPerformanceThresholds', () => {
    it('should return performance thresholds', () => {
      const configManager = new ConfigManager();
      const thresholds = configManager.getPerformanceThresholds();
      
      expect(thresholds).toEqual(DEFAULT_CONFIG.performanceThresholds);
    });
  });

  describe('getEnabledTestSuites', () => {
    it('should return only enabled test suites', () => {
      const customConfig: Partial<VerificationConfig> = {
        testSuites: [
          {
            name: 'unit-tests',
            type: 'unit',
            enabled: true,
            timeout: 30000,
            retries: 2,
          },
          {
            name: 'e2e-tests',
            type: 'e2e',
            enabled: false,
            timeout: 120000,
            retries: 3,
          },
        ],
      };

      const configManager = new ConfigManager(customConfig);
      const enabledSuites = configManager.getEnabledTestSuites();
      
      expect(enabledSuites).toHaveLength(1);
      expect(enabledSuites[0].name).toBe('unit-tests');
    });
  });

  describe('getCriticalDependencies', () => {
    it('should return only critical dependencies', () => {
      const configManager = new ConfigManager();
      const criticalDeps = configManager.getCriticalDependencies();
      
      const criticalCount = DEFAULT_CONFIG.externalDependencies.filter(dep => dep.critical).length;
      expect(criticalDeps).toHaveLength(criticalCount);
    });
  });

  describe('validateConfig', () => {
    it('should validate correct configuration', () => {
      const configManager = new ConfigManager();
      const validation = configManager.validateConfig();
      
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid performance thresholds', () => {
      const invalidConfig: Partial<VerificationConfig> = {
        performanceThresholds: {
          lcp: -100,
          fid: -50,
          cls: 2.0,
          lighthousePerformance: 150,
        },
      };

      const configManager = new ConfigManager(invalidConfig);
      const validation = configManager.validateConfig();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors).toContain('LCP threshold must be positive');
      expect(validation.errors).toContain('FID threshold must be positive');
      expect(validation.errors).toContain('CLS threshold must be between 0 and 1');
      expect(validation.errors).toContain('Lighthouse performance threshold must be between 0 and 100');
    });

    it('should detect invalid test suite configuration', () => {
      const invalidConfig: Partial<VerificationConfig> = {
        testSuites: [
          {
            name: '',
            type: 'unit',
            enabled: true,
            timeout: -1000,
            retries: -1,
          },
        ],
      };

      const configManager = new ConfigManager(invalidConfig);
      const validation = configManager.validateConfig();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Test suite 0 must have a name');
      expect(validation.errors).toContain('Test suite  timeout must be positive');
      expect(validation.errors).toContain('Test suite  retries must be non-negative');
    });

    it('should detect invalid external dependencies', () => {
      const invalidConfig: Partial<VerificationConfig> = {
        externalDependencies: [
          {
            name: '',
            url: '',
            timeout: -5000,
            critical: true,
          },
        ],
      };

      const configManager = new ConfigManager(invalidConfig);
      const validation = configManager.validateConfig();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('External dependency 0 must have a name');
      expect(validation.errors).toContain('External dependency  must have a URL');
      expect(validation.errors).toContain('External dependency  timeout must be positive');
    });
  });
});