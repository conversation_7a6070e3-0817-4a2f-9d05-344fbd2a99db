/**
 * Unit tests for accessibility validation system
 */

import { AccessibilityValidator, AccessibilityTestSuite } from '../accessibility';
import { ConfigManager } from '../config';
import { chromium, Browser, Page } from '@playwright/test';

// Mock Playwright
jest.mock('@playwright/test', () => ({
  chromium: {
    launch: jest.fn(),
  },
}));

// Mock axe-core/playwright
jest.mock('@axe-core/playwright', () => ({
  AxeBuilder: jest.fn().mockImplementation(() => ({
    withTags: jest.fn().mockReturnThis(),
    withRules: jest.fn().mockReturnThis(),
    disableRules: jest.fn().mockReturnThis(),
    analyze: jest.fn().mockResolvedValue({
      violations: [],
      incomplete: [],
      passes: [],
    }),
  })),
}));

describe('AccessibilityValidator', () => {
  let mockBrowser: jest.Mocked<Browser>;
  let mockPage: jest.Mocked<Page>;

  beforeEach(() => {
    mockPage = {
      goto: jest.fn().mockResolvedValue(undefined),
      waitForTimeout: jest.fn().mockResolvedValue(undefined),
      close: jest.fn().mockResolvedValue(undefined),
      locator: jest.fn().mockImplementation((selector: string) => {
        // Mock different selectors appropriately
        if (selector.includes('main') || selector.includes('[role="main"]')) {
          return {
            all: jest.fn().mockResolvedValue([{ evaluate: jest.fn() }]),
            count: jest.fn().mockResolvedValue(1),
          };
        }
        if (selector.includes('a, button, input')) {
          return {
            all: jest.fn().mockResolvedValue([
              { 
                focus: jest.fn(),
                evaluate: jest.fn().mockResolvedValue(true)
              }
            ]),
            count: jest.fn().mockResolvedValue(1),
            first: jest.fn().mockReturnValue({
              focus: jest.fn().mockResolvedValue(undefined),
              press: jest.fn().mockResolvedValue(undefined),
              evaluate: jest.fn().mockResolvedValue(true),
            }),
            nth: jest.fn().mockReturnValue({
              focus: jest.fn().mockResolvedValue(undefined),
              evaluate: jest.fn().mockResolvedValue(true),
            }),
          };
        }
        return {
          all: jest.fn().mockResolvedValue([]),
          count: jest.fn().mockResolvedValue(0),
        };
      }),
      keyboard: {
        press: jest.fn().mockResolvedValue(undefined),
      },
      evaluate: jest.fn().mockImplementation((fn) => {
        // Mock different evaluate calls
        const fnString = fn.toString();
        if (fnString.includes('document.activeElement')) {
          return Promise.resolve({
            tagName: 'BUTTON',
            id: 'test-button',
            className: 'btn',
            visible: true,
          });
        }
        return Promise.resolve('BUTTON');
      }),
    } as any;

    mockBrowser = {
      newPage: jest.fn().mockResolvedValue(mockPage),
      close: jest.fn().mockResolvedValue(undefined),
    } as any;

    (chromium.launch as jest.Mock).mockResolvedValue(mockBrowser);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create validator with default config', () => {
      const validator = new AccessibilityValidator();
      expect(validator).toBeInstanceOf(AccessibilityValidator);
    });

    it('should create validator with custom config', () => {
      const customConfig = {
        baseUrl: 'http://localhost:3000',
        wcagLevel: 'AAA' as const,
        pages: ['/custom'],
      };
      
      const validator = new AccessibilityValidator(customConfig);
      expect(validator).toBeInstanceOf(AccessibilityValidator);
    });
  });

  describe('validate', () => {
    it('should validate all configured pages', async () => {
      const validator = new AccessibilityValidator({
        pages: ['/', '/test'],
      });

      const result = await validator.validate();

      expect(result).toEqual({
        compliant: true,
        violations: [],
        warnings: [],
        testedPages: ['/', '/test'],
      });

      expect(mockBrowser.newPage).toHaveBeenCalledTimes(2);
      expect(mockPage.goto).toHaveBeenCalledTimes(2);
      expect(mockPage.goto).toHaveBeenCalledWith('http://localhost:4175/', { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      });
      expect(mockPage.goto).toHaveBeenCalledWith('http://localhost:4175/test', { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      });
    });

    it('should handle browser launch failure', async () => {
      (chromium.launch as jest.Mock).mockRejectedValue(new Error('Browser launch failed'));
      
      const validator = new AccessibilityValidator();
      
      await expect(validator.validate()).rejects.toThrow('Browser launch failed');
    });

    it('should close browser even if validation fails', async () => {
      mockPage.goto.mockRejectedValue(new Error('Navigation failed'));
      
      const validator = new AccessibilityValidator();
      
      const result = await validator.validate();
      
      // Should return result with navigation errors instead of throwing
      expect(result.compliant).toBe(false);
      expect(result.violations.length).toBeGreaterThan(0);
      expect(result.violations[0].rule).toBe('page-navigation');
      expect(mockBrowser.close).toHaveBeenCalled();
    });
  });

  describe('generateReport', () => {
    it('should generate report for compliant result', () => {
      const validator = new AccessibilityValidator();
      const result = {
        compliant: true,
        violations: [],
        warnings: [],
        testedPages: ['/'],
      };

      const report = validator.generateReport(result);

      expect(report).toContain('Overall Status: ✅ PASSED');
      expect(report).toContain('Pages Tested: /');
      expect(report).toContain('✅ All Tests Passed');
    });

    it('should generate report with violations', () => {
      const validator = new AccessibilityValidator();
      const result = {
        compliant: false,
        violations: [{
          rule: 'color-contrast',
          impact: 'serious' as const,
          element: 'div.text',
          description: 'Insufficient color contrast',
        }],
        warnings: [],
        testedPages: ['/'],
      };

      const report = validator.generateReport(result);

      expect(report).toContain('Overall Status: ❌ FAILED');
      expect(report).toContain('## Violations (Must Fix)');
      expect(report).toContain('color-contrast (serious)');
      expect(report).toContain('Insufficient color contrast');
      expect(report).toContain('**Remediation:**');
    });

    it('should generate report with warnings', () => {
      const validator = new AccessibilityValidator();
      const result = {
        compliant: true,
        violations: [],
        warnings: [{
          rule: 'manual-check',
          element: 'button',
          description: 'Manual review needed',
        }],
        testedPages: ['/'],
      };

      const report = validator.generateReport(result);

      expect(report).toContain('## Warnings (Should Fix)');
      expect(report).toContain('manual-check');
      expect(report).toContain('Manual review needed');
    });
  });
});

describe('AccessibilityTestSuite', () => {
  let configManager: ConfigManager;

  beforeEach(() => {
    configManager = new ConfigManager();
  });

  describe('constructor', () => {
    it('should create test suite with config manager', () => {
      const testSuite = new AccessibilityTestSuite(configManager);
      expect(testSuite).toBeInstanceOf(AccessibilityTestSuite);
    });
  });

  describe('execute', () => {
    it('should execute accessibility validation and return test result', async () => {
      const testSuite = new AccessibilityTestSuite(configManager);
      
      // Mock the validator
      const mockValidator = {
        validate: jest.fn().mockResolvedValue({
          compliant: true,
          violations: [],
          warnings: [],
          testedPages: ['/'],
        }),
      };

      // Replace the validator instance
      (testSuite as any).validator = mockValidator;

      const result = await testSuite.execute();

      expect(result).toEqual({
        passed: true,
        duration: expect.any(Number),
        testCount: 1,
        failures: [],
      });

      expect(mockValidator.validate).toHaveBeenCalled();
    });

    it('should handle validation failure', async () => {
      const testSuite = new AccessibilityTestSuite(configManager);
      
      const mockValidator = {
        validate: jest.fn().mockResolvedValue({
          compliant: false,
          violations: [{
            rule: 'test-rule',
            impact: 'serious',
            element: 'div',
            description: 'Test violation',
          }],
          warnings: [],
          testedPages: ['/'],
        }),
      };

      (testSuite as any).validator = mockValidator;

      const result = await testSuite.execute();

      expect(result).toEqual({
        passed: false,
        duration: expect.any(Number),
        testCount: 1,
        failures: [{
          testName: 'test-rule - div',
          error: 'Test violation',
          duration: 0,
        }],
      });
    });

    it('should handle validator exception', async () => {
      const testSuite = new AccessibilityTestSuite(configManager);
      
      const mockValidator = {
        validate: jest.fn().mockRejectedValue(new Error('Validation failed')),
      };

      (testSuite as any).validator = mockValidator;

      const result = await testSuite.execute();

      expect(result).toEqual({
        passed: false,
        duration: expect.any(Number),
        testCount: 0,
        failures: [{
          testName: 'Accessibility validation',
          error: 'Test suite failed: Error: Validation failed',
          duration: expect.any(Number),
        }],
      });
    });
  });
});

describe('Accessibility Integration', () => {
  it('should integrate with ConfigManager for WCAG level', () => {
    const config = new ConfigManager({
      accessibilityLevel: 'AAA',
    });

    const testSuite = new AccessibilityTestSuite(config);
    expect(testSuite).toBeInstanceOf(AccessibilityTestSuite);
  });

  it('should use timeout from test suite config', () => {
    const config = new ConfigManager({
      testSuites: [{
        name: 'accessibility-tests',
        type: 'accessibility',
        enabled: true,
        timeout: 120000,
        retries: 3,
      }],
    });

    const testSuite = new AccessibilityTestSuite(config);
    expect(testSuite).toBeInstanceOf(AccessibilityTestSuite);
  });
});