/**
 * Unit tests for the performance monitoring system
 */

import { PerformanceMonitor, createPerformanceMonitor } from '../performance';
import { createConfigManager } from '../config';
import { PerformanceMetrics, VerificationConfig } from '../types';

// Mock playwright-lighthouse
jest.mock('playwright-lighthouse', () => ({
  playAudit: jest.fn(),
}));

// Mock Playwright page
const mockPage = {
  goto: jest.fn(),
  addInitScript: jest.fn(),
  waitForLoadState: jest.fn(),
  waitForTimeout: jest.fn(),
  evaluate: jest.fn(),
} as any;

const mockBrowser = {
  newPage: jest.fn().mockResolvedValue(mockPage),
} as any;

describe('PerformanceMonitor', () => {
  let performanceMonitor: PerformanceMonitor;
  let config: VerificationConfig;

  beforeEach(() => {
    const configManager = createConfigManager();
    config = configManager.getConfig();
    performanceMonitor = createPerformanceMonitor(config);

    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    mockPage.goto.mockResolvedValue(undefined);
    mockPage.addInitScript.mockResolvedValue(undefined);
    mockPage.waitForLoadState.mockResolvedValue(undefined);
    mockPage.waitForTimeout.mockResolvedValue(undefined);
    mockPage.close = jest.fn().mockResolvedValue(undefined);
  });

  describe('constructor', () => {
    it('should create a performance monitor with config', () => {
      expect(performanceMonitor).toBeInstanceOf(PerformanceMonitor);
    });
  });

  describe('runPerformanceTest', () => {
    it('should run a complete performance test', async () => {
      const url = 'http://localhost:8080/';
      
      // Mock Core Web Vitals collection
      mockPage.evaluate.mockResolvedValueOnce({
        lcp: 1500,
        fid: 50,
        cls: 0.05,
        fcp: 1200,
      });

      // Mock performance API fallback
      mockPage.evaluate.mockResolvedValueOnce({
        fcp: 1200,
        navigationStart: 0,
        loadComplete: 2000,
      });

      // Mock Lighthouse audit
      const { playAudit } = require('playwright-lighthouse');
      playAudit.mockResolvedValue({
        lhr: {
          categories: {
            performance: { score: 0.95 },
            accessibility: { score: 0.92 },
            'best-practices': { score: 0.88 },
            seo: { score: 0.90 },
          },
        },
      });

      const result = await performanceMonitor.runPerformanceTest(mockPage, url);

      expect(result.success).toBe(true);
      expect(result.url).toBe(url);
      expect(result.metrics.lcp).toBe(1500);
      expect(result.metrics.fid).toBe(50);
      expect(result.metrics.cls).toBe(0.05);
      expect(result.metrics.lighthouse.performance).toBe(95);
      expect(result.violations).toHaveLength(0);
      expect(typeof result.duration).toBe('number');
    });

    it('should detect performance violations', async () => {
      const url = 'http://localhost:8080/';
      
      // Mock poor performance metrics
      mockPage.evaluate.mockResolvedValueOnce({
        lcp: 4000, // Exceeds 2500ms threshold
        fid: 200,  // Exceeds 100ms threshold
        cls: 0.2,  // Exceeds 0.1 threshold
        fcp: 3000,
      });

      mockPage.evaluate.mockResolvedValueOnce({
        fcp: 3000,
        navigationStart: 0,
        loadComplete: 4000,
      });

      // Mock poor Lighthouse score
      const { playAudit } = require('playwright-lighthouse');
      playAudit.mockResolvedValue({
        lhr: {
          categories: {
            performance: { score: 0.70 }, // Below 90 threshold
            accessibility: { score: 0.85 },
            'best-practices': { score: 0.80 },
            seo: { score: 0.75 },
          },
        },
      });

      const result = await performanceMonitor.runPerformanceTest(mockPage, url);

      expect(result.success).toBe(false);
      expect(result.violations.length).toBeGreaterThan(0);
      
      // Check for specific violations
      const violationMetrics = result.violations.map(v => v.metric);
      expect(violationMetrics).toContain('LCP');
      expect(violationMetrics).toContain('FID');
      expect(violationMetrics).toContain('CLS');
      expect(violationMetrics).toContain('Lighthouse Performance');
    });

    it('should handle Lighthouse audit failures gracefully', async () => {
      const url = 'http://localhost:8080/';
      
      // Mock Core Web Vitals collection
      mockPage.evaluate.mockResolvedValueOnce({
        lcp: 1500,
        fid: 50,
        cls: 0.05,
        fcp: 1200,
      });

      mockPage.evaluate.mockResolvedValueOnce({
        fcp: 1200,
        navigationStart: 0,
        loadComplete: 2000,
      });

      // Mock Lighthouse audit failure
      const { playAudit } = require('playwright-lighthouse');
      playAudit.mockRejectedValue(new Error('Lighthouse failed'));

      const result = await performanceMonitor.runPerformanceTest(mockPage, url);

      // Should still complete with fallback Lighthouse scores
      expect(result.metrics.lighthouse.performance).toBe(0);
      expect(result.metrics.lighthouse.accessibility).toBe(0);
      expect(result.metrics.lighthouse.bestPractices).toBe(0);
      expect(result.metrics.lighthouse.seo).toBe(0);
    });

    it('should handle page navigation failures', async () => {
      const url = 'http://localhost:8080/invalid';
      
      // Mock navigation failure
      mockPage.goto.mockRejectedValue(new Error('Navigation failed'));

      await expect(
        performanceMonitor.runPerformanceTest(mockPage, url)
      ).rejects.toThrow('Performance test failed for http://localhost:8080/invalid');
    });
  });

  describe('testCriticalPages', () => {
    it('should test multiple pages successfully', async () => {
      const pages = ['/', '/team-sales', '/harbor-city'];
      const baseUrl = 'http://localhost:8080';

      // Mock successful page creation and closing
      mockBrowser.newPage.mockResolvedValue(mockPage);

      // Mock Core Web Vitals for each page
      mockPage.evaluate
        .mockResolvedValueOnce({ lcp: 1500, fid: 50, cls: 0.05, fcp: 1200 })
        .mockResolvedValueOnce({ fcp: 1200, navigationStart: 0, loadComplete: 2000 })
        .mockResolvedValueOnce({ lcp: 1600, fid: 60, cls: 0.06, fcp: 1300 })
        .mockResolvedValueOnce({ fcp: 1300, navigationStart: 0, loadComplete: 2100 })
        .mockResolvedValueOnce({ lcp: 1400, fid: 40, cls: 0.04, fcp: 1100 })
        .mockResolvedValueOnce({ fcp: 1100, navigationStart: 0, loadComplete: 1900 });

      // Mock Lighthouse audits
      const { playAudit } = require('playwright-lighthouse');
      playAudit.mockResolvedValue({
        lhr: {
          categories: {
            performance: { score: 0.95 },
            accessibility: { score: 0.92 },
            'best-practices': { score: 0.88 },
            seo: { score: 0.90 },
          },
        },
      });

      const results = await performanceMonitor.testCriticalPages(mockBrowser, pages, baseUrl);

      expect(results).toHaveLength(3);
      expect(results.every(result => result.success)).toBe(true);
      expect(mockBrowser.newPage).toHaveBeenCalledTimes(3);
      expect(mockPage.close).toHaveBeenCalledTimes(3);
    });

    it('should handle individual page test failures', async () => {
      const pages = ['/', '/invalid'];
      const baseUrl = 'http://localhost:8080';

      mockBrowser.newPage.mockResolvedValue(mockPage);

      // First page succeeds
      mockPage.evaluate
        .mockResolvedValueOnce({ lcp: 1500, fid: 50, cls: 0.05, fcp: 1200 })
        .mockResolvedValueOnce({ fcp: 1200, navigationStart: 0, loadComplete: 2000 });

      const { playAudit } = require('playwright-lighthouse');
      playAudit
        .mockResolvedValueOnce({
          lhr: {
            categories: {
              performance: { score: 0.95 },
              accessibility: { score: 0.92 },
              'best-practices': { score: 0.88 },
              seo: { score: 0.90 },
            },
          },
        })
        .mockRejectedValueOnce(new Error('Second page failed'));

      // Second page fails during navigation
      mockPage.goto
        .mockResolvedValueOnce(undefined) // First page succeeds
        .mockRejectedValueOnce(new Error('Navigation failed')); // Second page fails

      const results = await performanceMonitor.testCriticalPages(mockBrowser, pages, baseUrl);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[1].violations[0].metric).toBe('Test Execution');
    });
  });

  describe('generatePerformanceReport', () => {
    it('should generate a comprehensive performance report', () => {
      const mockResults = [
        {
          success: true,
          metrics: {
            lcp: 1500,
            fid: 50,
            cls: 0.05,
            fcp: 1200,
            lighthouse: { performance: 95, accessibility: 92, bestPractices: 88, seo: 90 },
          },
          violations: [],
          duration: 5000,
          url: 'http://localhost:8080/',
        },
        {
          success: true,
          metrics: {
            lcp: 1600,
            fid: 60,
            cls: 0.06,
            fcp: 1300,
            lighthouse: { performance: 93, accessibility: 90, bestPractices: 85, seo: 88 },
          },
          violations: [],
          duration: 5500,
          url: 'http://localhost:8080/team-sales',
        },
      ];

      const report = performanceMonitor.generatePerformanceReport(mockResults);

      expect(report.overallSuccess).toBe(true);
      expect(report.totalViolations).toBe(0);
      expect(report.pageResults).toHaveLength(2);
      
      // Check average calculations
      expect(report.averageMetrics.lcp).toBe(1550); // (1500 + 1600) / 2
      expect(report.averageMetrics.fid).toBe(55);   // (50 + 60) / 2
      expect(report.averageMetrics.cls).toBe(0.055); // (0.05 + 0.06) / 2
      expect(report.averageMetrics.lighthouse.performance).toBe(94); // (95 + 93) / 2
    });

    it('should handle reports with violations', () => {
      const mockResults = [
        {
          success: false,
          metrics: {
            lcp: 3000,
            fid: 150,
            cls: 0.15,
            fcp: 2500,
            lighthouse: { performance: 70, accessibility: 80, bestPractices: 75, seo: 85 },
          },
          violations: [
            { metric: 'LCP', actual: 3000, threshold: 2500, severity: 'error' as const },
            { metric: 'FID', actual: 150, threshold: 100, severity: 'error' as const },
          ],
          duration: 8000,
          url: 'http://localhost:8080/slow-page',
        },
      ];

      const report = performanceMonitor.generatePerformanceReport(mockResults);

      expect(report.overallSuccess).toBe(false);
      expect(report.totalViolations).toBe(2);
      expect(report.averageMetrics.lcp).toBe(3000);
      expect(report.averageMetrics.lighthouse.performance).toBe(70);
    });

    it('should handle empty results gracefully', () => {
      const report = performanceMonitor.generatePerformanceReport([]);

      expect(report.overallSuccess).toBe(true);
      expect(report.totalViolations).toBe(0);
      expect(report.pageResults).toHaveLength(0);
      expect(report.averageMetrics.lcp).toBe(0);
    });
  });

  describe('createPerformanceMonitor', () => {
    it('should create a performance monitor instance', () => {
      const configManager = createConfigManager();
      const monitor = createPerformanceMonitor(configManager.getConfig());
      
      expect(monitor).toBeInstanceOf(PerformanceMonitor);
    });
  });

  describe('threshold validation', () => {
    it('should correctly identify warning vs error violations', async () => {
      const url = 'http://localhost:8080/';
      
      // Mock metrics that trigger both warnings and errors
      mockPage.evaluate.mockResolvedValueOnce({
        lcp: 3750, // 1.5x threshold (2500) = warning
        fid: 250,  // 2.5x threshold (100) = error
        cls: 0.15, // 1.5x threshold (0.1) = warning
        fcp: 2000,
      });

      mockPage.evaluate.mockResolvedValueOnce({
        fcp: 2000,
        navigationStart: 0,
        loadComplete: 3750,
      });

      // Mock Lighthouse score that triggers error
      const { playAudit } = require('playwright-lighthouse');
      playAudit.mockResolvedValue({
        lhr: {
          categories: {
            performance: { score: 0.65 }, // 0.65 < 0.8 * 90 = error
            accessibility: { score: 0.85 },
            'best-practices': { score: 0.80 },
            seo: { score: 0.75 },
          },
        },
      });

      const result = await performanceMonitor.runPerformanceTest(mockPage, url);

      expect(result.success).toBe(false);
      
      const warningViolations = result.violations.filter(v => v.severity === 'warning');
      const errorViolations = result.violations.filter(v => v.severity === 'error');
      
      expect(warningViolations.length).toBeGreaterThan(0);
      expect(errorViolations.length).toBeGreaterThan(0);
      
      // LCP and CLS should be warnings (1.5x threshold)
      expect(warningViolations.some(v => v.metric === 'LCP')).toBe(true);
      expect(warningViolations.some(v => v.metric === 'CLS')).toBe(true);
      
      // FID and Lighthouse should be errors
      expect(errorViolations.some(v => v.metric === 'FID')).toBe(true);
      expect(errorViolations.some(v => v.metric === 'Lighthouse Performance')).toBe(true);
    });
  });
});