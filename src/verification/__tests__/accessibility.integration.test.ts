/**
 * Integration tests for accessibility validation system
 * Tests the complete accessibility validation workflow
 */

import { AccessibilityValidator, AccessibilityTestSuite } from '../accessibility';
import { ConfigManager } from '../config';

describe('Accessibility Integration Tests', () => {
  let validator: AccessibilityValidator;
  let configManager: ConfigManager;

  beforeEach(() => {
    configManager = new ConfigManager({
      accessibilityLevel: 'AA',
      testSuites: [{
        name: 'accessibility-tests',
        type: 'accessibility',
        enabled: true,
        timeout: 30000,
        retries: 2,
      }],
    });

    validator = new AccessibilityValidator({
      baseUrl: 'http://localhost:4175',
      pages: ['/'],
      wcagLevel: 'AA',
      timeout: 15000,
      keyboardNavigation: true,
      colorContrast: true,
      screenReader: true,
    });
  });

  describe('WCAG 2.1 AA Compliance', () => {
    it('should validate WCAG 2.1 AA compliance requirements', async () => {
      // This test verifies the core WCAG compliance checking
      expect(validator).toBeInstanceOf(AccessibilityValidator);
      
      // Test configuration
      const config = (validator as any).config;
      expect(config.wcagLevel).toBe('AA');
      expect(config.keyboardNavigation).toBe(true);
      expect(config.colorContrast).toBe(true);
      expect(config.screenReader).toBe(true);
    });
  });

  describe('Remediation Guidance', () => {
    it('should provide comprehensive remediation guidance', () => {
      const testRules = [
        'color-contrast',
        'image-alt',
        'label',
        'button-name',
        'keyboard-navigation',
        'screen-reader-compatibility',
      ];

      testRules.forEach(rule => {
        const guidance = (validator as any).getRemediationGuidance(rule);
        expect(guidance).toBeTruthy();
        expect(guidance.length).toBeGreaterThan(20);
        expect(guidance).not.toBe('Review WCAG 2.1 guidelines for this rule and implement appropriate fixes. Visit https://www.w3.org/WAI/WCAG21/quickref/ for detailed guidance.');
      });
    });
  });
});