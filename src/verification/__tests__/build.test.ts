/**
 * Unit tests for build verification engine
 */

import { BuildVerificationEngine, createBuildVerificationEngine } from '../build';
import { BuildError, BuildWarning, BuildVerificationResult } from '../types';
import { promises as fs } from 'fs';
import { spawn } from 'child_process';

// Mock child_process
jest.mock('child_process');
const mockSpawn = spawn as jest.MockedFunction<typeof spawn>;

// Mock fs promises
jest.mock('fs', () => ({
  promises: {
    rm: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn(),
  },
}));

const mockFs = fs as jest.Mocked<typeof fs>;

describe('BuildVerificationEngine', () => {
  let engine: BuildVerificationEngine;
  let mockProcess: any;

  beforeEach(() => {
    engine = new BuildVerificationEngine();
    
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default mock process
    mockProcess = {
      stdout: {
        on: jest.fn(),
      },
      stderr: {
        on: jest.fn(),
      },
      on: jest.fn(),
    };
    
    mockSpawn.mockReturnValue(mockProcess as any);
  });

  describe('verify', () => {
    it('should return successful result when build succeeds', async () => {
      // Setup successful build
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          // Add small delay to ensure buildTime > 0
          setTimeout(() => callback(0), 1); // Success exit code
        }
      });

      // Mock build metrics
      mockFs.readdir.mockResolvedValue(['index.js', 'vendor.js', 'styles.css'] as any);
      mockFs.stat.mockResolvedValue({ size: 1024 } as any);

      const result = await engine.verify();

      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.buildTime).toBeGreaterThanOrEqual(0);
      expect(result.outputSize.total).toBeGreaterThan(0);
    });

    it('should return failed result when build fails', async () => {
      // Setup failed build
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          // Add small delay to ensure buildTime > 0
          setTimeout(() => callback(1), 1); // Failure exit code
        }
      });

      const result = await engine.verify();

      expect(result.success).toBe(false);
      expect(result.buildTime).toBeGreaterThanOrEqual(0);
      expect(result.outputSize.total).toBe(0);
    });

    it('should handle build process errors', async () => {
      // Setup process error
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'error') {
          callback(new Error('Process spawn failed'));
        }
      });

      const result = await engine.verify();

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toContain('Process spawn failed');
    });

    it('should clean build directory before building', async () => {
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(0);
        }
      });

      await engine.verify();

      expect(mockFs.rm).toHaveBeenCalledWith(
        expect.stringContaining('dist'),
        { recursive: true, force: true }
      );
    });
  });

  describe('error parsing', () => {
    it('should parse TypeScript errors correctly', async () => {
      const tsErrorOutput = 'src/components/Test.tsx(15,23): error TS2345: Argument of type string is not assignable to parameter of type number';
      
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callback(tsErrorOutput);
        }
      });

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(1); // Build failed
        }
      });

      const result = await engine.verify();

      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toEqual({
        file: 'src/components/Test.tsx',
        line: 15,
        column: 23,
        message: 'Argument of type string is not assignable to parameter of type number',
        type: 'typescript',
      });
    });

    it('should parse TypeScript warnings correctly', async () => {
      const tsWarningOutput = 'src/utils/helper.ts(8,12): warning TS6133: Variable is declared but never used';
      
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callback(tsWarningOutput);
        }
      });

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(0); // Build succeeded
        }
      });

      const result = await engine.verify();

      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0]).toEqual({
        file: 'src/utils/helper.ts',
        line: 8,
        column: 12,
        message: 'Variable is declared but never used',
        type: 'typescript',
      });
    });

    it('should parse Vite bundling errors correctly', async () => {
      const viteErrorOutput = '[vite:esbuild] Transform failed with 1 error';
      
      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callback(viteErrorOutput);
        }
      });

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(1);
        }
      });

      const result = await engine.verify();

      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('bundling');
      expect(result.errors[0].message).toContain('Transform failed');
    });

    it('should parse Vite warnings correctly', async () => {
      const viteWarningOutput = 'warning: Large chunk size detected';
      
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callback(viteWarningOutput);
        }
      });

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(0);
        }
      });

      const result = await engine.verify();

      expect(result.warnings).toHaveLength(1);
      expect(result.warnings[0].type).toBe('bundling');
      expect(result.warnings[0].message).toContain('Large chunk size');
    });
  });

  describe('build metrics collection', () => {
    it('should collect chunk information correctly', async () => {
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(0);
        }
      });

      // Mock file system for metrics collection
      mockFs.readdir.mockResolvedValue([
        'index.abc123def.js',
        'vendor.xyz789ghi.js',
        'styles.jkl456mno.css'
      ] as any);
      
      mockFs.stat.mockImplementation((filePath: string) => {
        const size = filePath.includes('vendor') ? 2048 : 1024;
        return Promise.resolve({ size } as any);
      });

      const result = await engine.verify();

      expect(result.outputSize.chunks).toHaveLength(3);
      expect(result.outputSize.total).toBe(4096); // 1024 + 2048 + 1024
      
      const indexChunk = result.outputSize.chunks.find(c => c.name.includes('index'));
      expect(indexChunk).toBeDefined();
      expect(indexChunk?.size).toBe(1024);
    });

    it('should handle metrics collection errors gracefully', async () => {
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(0);
        }
      });

      // Mock readdir to throw error
      mockFs.readdir.mockRejectedValue(new Error('Directory not found'));

      const result = await engine.verify();

      expect(result.success).toBe(true); // Build still succeeds
      expect(result.outputSize.total).toBe(0);
      expect(result.outputSize.chunks).toHaveLength(0);
    });
  });

  describe('utility methods', () => {
    it('should extract chunk names correctly', async () => {
      const engine = new BuildVerificationEngine();
      
      // Access private method through any cast for testing
      const extractChunkName = (engine as any).extractChunkName.bind(engine);
      
      expect(extractChunkName('index.abc123def.js')).toBe('index.js');
      expect(extractChunkName('vendor.xyz789ghi.css')).toBe('vendor.css');
      expect(extractChunkName('chunk.a1b2c3d4e5f6g7h8.js')).toBe('chunk.js');
    });

    it('should provide detailed error information', async () => {
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callback('src/test.ts(10,5): error TS2345: Type error');
        }
      });

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callback(1);
        }
      });

      await engine.verify();
      const detailedErrors = engine.getDetailedErrors();

      expect(detailedErrors).toHaveLength(1);
      expect(detailedErrors[0]).toBe('TYPESCRIPT: src/test.ts:10:5 - Type error');
    });

    it('should check size thresholds correctly', () => {
      const result: BuildVerificationResult = {
        success: true,
        buildTime: 1000,
        errors: [],
        warnings: [],
        outputSize: {
          total: 3 * 1024 * 1024, // 3MB
          chunks: [],
        },
      };

      expect(engine.checkSizeThresholds(result, 5 * 1024 * 1024)).toBe(true); // Under 5MB
      expect(engine.checkSizeThresholds(result, 2 * 1024 * 1024)).toBe(false); // Over 2MB
    });

    it('should generate build summary correctly', () => {
      const result: BuildVerificationResult = {
        success: true,
        buildTime: 2500,
        errors: [],
        warnings: [{ file: 'test.ts', line: 1, column: 1, message: 'Warning', type: 'typescript' }],
        outputSize: {
          total: 1.5 * 1024 * 1024, // 1.5MB
          chunks: [
            { name: 'index.js', size: 1024, modules: [] },
            { name: 'vendor.js', size: 2048, modules: [] },
          ],
        },
      };

      const summary = engine.getBuildSummary(result);

      expect(summary).toContain('Build SUCCESS');
      expect(summary).toContain('Time: 2500ms');
      expect(summary).toContain('Errors: 0');
      expect(summary).toContain('Warnings: 1');
      expect(summary).toContain('Output Size: 1.50MB');
      expect(summary).toContain('Chunks: 2');
    });
  });

  describe('factory function', () => {
    it('should create build verification engine instance', () => {
      const engine = createBuildVerificationEngine();
      expect(engine).toBeInstanceOf(BuildVerificationEngine);
    });
  });
});