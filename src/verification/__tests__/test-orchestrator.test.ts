/**
 * Unit tests for Test Orchestrator Framework
 */

import { TestOrchestrator, TestOrchestratorConfig } from '../test-orchestrator';
import { TestResult, TestSuite } from '../types';

// Mock child_process
jest.mock('child_process', () => ({
  spawn: jest.fn()
}));

// Mock fs promises
jest.mock('fs', () => ({
  promises: {
    writeFile: jest.fn()
  }
}));

import { spawn } from 'child_process';
import { promises as fs } from 'fs';

const mockSpawn = spawn as jest.MockedFunction<typeof spawn>;
const mockWriteFile = fs.writeFile as jest.MockedFunction<typeof fs.writeFile>;

describe('TestOrchestrator', () => {
  let orchestrator: TestOrchestrator;
  let mockConfig: TestOrchestratorConfig;

  beforeEach(() => {
    mockConfig = {
      testSuites: [
        {
          name: 'unit',
          type: 'unit',
          enabled: true,
          timeout: 30000,
          retries: 0
        },
        {
          name: 'integration',
          type: 'integration',
          enabled: true,
          timeout: 45000,
          retries: 1
        },
        {
          name: 'e2e',
          type: 'e2e',
          enabled: false,
          timeout: 60000,
          retries: 2
        }
      ],
      jestConfig: 'jest.config.js',
      playwrightConfig: 'playwright.config.ts',
      outputDir: './test-results',
      parallel: false,
      timeout: 300000
    };

    orchestrator = new TestOrchestrator(mockConfig);
    
    // Reset mocks
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with provided configuration', () => {
      expect(orchestrator).toBeInstanceOf(TestOrchestrator);
    });

    it('should initialize test suites based on configuration', () => {
      // Test suites are initialized in the constructor
      // We can verify this by checking if executeAll works
      expect(orchestrator).toBeDefined();
    });
  });

  describe('executeAll', () => {
    it('should execute all enabled test suites sequentially', async () => {
      // Mock successful Jest execution
      const mockProcess = {
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('{"numTotalTests": 5, "runTime": 1000, "success": true}');
            }
          })
        },
        stderr: {
          on: jest.fn()
        },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            callback(0); // Success exit code
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess as any);

      const results = await orchestrator.executeAll();

      // Should execute only enabled test suites (unit and integration)
      expect(results).toHaveLength(2);
      expect(mockSpawn).toHaveBeenCalledTimes(2);
    });

    it('should execute test suites in parallel when configured', async () => {
      const parallelConfig = { ...mockConfig, parallel: true };
      const parallelOrchestrator = new TestOrchestrator(parallelConfig);

      // Mock successful Jest execution
      const mockProcess = {
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('{"numTotalTests": 3, "runTime": 500, "success": true}');
            }
          })
        },
        stderr: {
          on: jest.fn()
        },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            callback(0);
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess as any);

      const results = await parallelOrchestrator.executeAll();

      expect(results).toHaveLength(2);
      expect(mockSpawn).toHaveBeenCalledTimes(2);
    });

    it('should handle test suite execution failures', async () => {
      // Mock failed Jest execution
      const mockProcess = {
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('{"numTotalTests": 2, "runTime": 800, "success": false}');
            }
          })
        },
        stderr: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('Test failed with error');
            }
          })
        },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            callback(1); // Failure exit code
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess as any);

      const results = await orchestrator.executeAll();

      expect(results).toHaveLength(2);
      expect(results[0].passed).toBe(false);
      expect(results[1].passed).toBe(false);
    });

    it('should bail on first failure when bail option is true', async () => {
      // Mock first test suite failure
      const mockProcess = {
        stdout: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('{"numTotalTests": 2, "runTime": 800, "success": false}');
            }
          })
        },
        stderr: {
          on: jest.fn((event, callback) => {
            if (event === 'data') {
              callback('Test failed');
            }
          })
        },
        on: jest.fn((event, callback) => {
          if (event === 'close') {
            callback(1);
          }
        })
      };

      mockSpawn.mockReturnValue(mockProcess as any);

      const results = await orchestrator.executeAll({ bail: true });

      // Should only execute one test suite before bailing
      expect(results).toHaveLength(1);
      expect(results[0].passed).toBe(false);
      expect(mockSpawn).toHaveBeenCalledTimes(1);
    });
  });

  describe('aggregateResults', () => {
    it('should correctly aggregate test results', () => {
      const testResults: TestResult[] = [
        {
          passed: true,
          duration: 1000,
          testCount: 5,
          failures: []
        },
        {
          passed: false,
          duration: 2000,
          testCount: 3,
          failures: [
            {
              testName: 'failing test',
              error: 'Test error',
              duration: 100
            }
          ]
        }
      ];

      const aggregated = orchestrator.aggregateResults(testResults);

      expect(aggregated.totalTests).toBe(8);
      expect(aggregated.totalPassed).toBe(7);
      expect(aggregated.totalFailed).toBe(1);
      expect(aggregated.totalDuration).toBe(3000);
      expect(aggregated.overallPassed).toBe(false);
    });

    it('should group failures by test suite type', () => {
      const testResults: TestResult[] = [
        {
          passed: false,
          duration: 1000,
          testCount: 2,
          failures: [
            {
              testName: 'unit test failure',
              error: 'Unit test error',
              duration: 50
            }
          ]
        },
        {
          passed: false,
          duration: 2000,
          testCount: 1,
          failures: [
            {
              testName: 'integration test failure',
              error: 'Integration test error',
              duration: 100
            }
          ]
        }
      ];

      const aggregated = orchestrator.aggregateResults(testResults);

      expect(aggregated.failuresByType).toHaveProperty('unit');
      expect(aggregated.failuresByType).toHaveProperty('integration');
      expect(aggregated.failuresByType.unit).toHaveLength(1);
      expect(aggregated.failuresByType.integration).toHaveLength(1);
    });
  });

  describe('generateReport', () => {
    it('should generate JSON report with test results', async () => {
      const testResults: TestResult[] = [
        {
          passed: true,
          duration: 1000,
          testCount: 5,
          failures: []
        }
      ];

      mockWriteFile.mockResolvedValue(undefined);

      const reportJson = await orchestrator.generateReport(testResults, './test-report.json');

      const report = JSON.parse(reportJson);
      
      expect(report).toHaveProperty('timestamp');
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('results');
      expect(report.summary.totalTests).toBe(5);
      expect(report.summary.overallPassed).toBe(true);
      expect(report.results).toHaveLength(1);

      expect(mockWriteFile).toHaveBeenCalledWith('./test-report.json', reportJson);
    });

    it('should return report JSON without writing to file when no output path provided', async () => {
      const testResults: TestResult[] = [
        {
          passed: false,
          duration: 2000,
          testCount: 3,
          failures: [
            {
              testName: 'test failure',
              error: 'Error message',
              duration: 100
            }
          ]
        }
      ];

      const reportJson = await orchestrator.generateReport(testResults);

      const report = JSON.parse(reportJson);
      
      expect(report.summary.totalFailed).toBe(1);
      expect(report.summary.overallPassed).toBe(false);
      expect(mockWriteFile).not.toHaveBeenCalled();
    });
  });

  describe('executeSuite', () => {
    it('should execute a specific test suite and return results', async () => {
      const mockTestSuite: TestSuite = {
        name: 'test-suite',
        type: 'unit',
        execute: jest.fn().mockResolvedValue({
          passed: true,
          duration: 1500,
          testCount: 10,
          failures: []
        })
      };

      const result = await orchestrator.executeSuite(mockTestSuite);

      expect(result.passed).toBe(true);
      expect(result.testCount).toBe(10);
      expect(result.failures).toHaveLength(0);
      expect(mockTestSuite.execute).toHaveBeenCalledTimes(1);
    });

    it('should handle test suite execution errors', async () => {
      const mockTestSuite: TestSuite = {
        name: 'failing-suite',
        type: 'unit',
        execute: jest.fn().mockRejectedValue(new Error('Suite execution failed'))
      };

      const result = await orchestrator.executeSuite(mockTestSuite);

      expect(result.passed).toBe(false);
      expect(result.failures).toHaveLength(1);
      expect(result.failures[0].error).toBe('Suite execution failed');
    });
  });
});

// Mock Test Suite for testing
class MockTestSuite implements TestSuite {
  name: string;
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility';
  private shouldPass: boolean;

  constructor(name: string, type: TestSuite['type'], shouldPass: boolean = true) {
    this.name = name;
    this.type = type;
    this.shouldPass = shouldPass;
  }

  async execute(): Promise<TestResult> {
    return {
      passed: this.shouldPass,
      duration: 1000,
      testCount: this.shouldPass ? 5 : 3,
      failures: this.shouldPass ? [] : [
        {
          testName: `${this.name} failure`,
          error: 'Mock test failure',
          duration: 100
        }
      ]
    };
  }
}