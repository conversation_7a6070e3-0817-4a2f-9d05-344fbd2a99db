/**
 * Unit tests for Test Configuration Management System
 */

import { TestConfigManager } from '../test-config';
import { TestOrchestratorConfig } from '../test-orchestrator';

// Mock fs promises
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn()
  }
}));

import { promises as fs } from 'fs';

const mockReadFile = fs.readFile as jest.MockedFunction<typeof fs.readFile>;
const mockWriteFile = fs.writeFile as jest.MockedFunction<typeof fs.writeFile>;

describe('TestConfigManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('loadConfig', () => {
    it('should return default configuration when no options provided', async () => {
      const config = await TestConfigManager.loadConfig();

      expect(config).toHaveProperty('testSuites');
      expect(config.testSuites).toHaveLength(3);
      expect(config.testSuites[0].name).toBe('unit');
      expect(config.testSuites[1].name).toBe('integration');
      expect(config.testSuites[2].name).toBe('e2e');
      expect(config.parallel).toBe(false);
      expect(config.timeout).toBe(300000);
    });

    it('should apply environment-specific overrides for development', async () => {
      const config = await TestConfigManager.loadConfig({ environment: 'development' });

      expect(config.parallel).toBe(false);
      expect(config.timeout).toBe(600000);
      
      // Integration and E2E should be disabled in development
      const integrationSuite = config.testSuites.find(suite => suite.name === 'integration');
      const e2eSuite = config.testSuites.find(suite => suite.name === 'e2e');
      
      expect(integrationSuite?.enabled).toBe(false);
      expect(e2eSuite?.enabled).toBe(false);
    });

    it('should apply environment-specific overrides for production', async () => {
      const config = await TestConfigManager.loadConfig({ environment: 'production' });

      expect(config.parallel).toBe(true);
      expect(config.timeout).toBe(300000);
      
      // All suites should be enabled in production with higher retry counts
      config.testSuites.forEach(suite => {
        expect(suite.enabled).toBe(true);
        expect(suite.retries).toBeGreaterThan(0);
      });
    });

    it('should apply environment-specific overrides for test', async () => {
      const config = await TestConfigManager.loadConfig({ environment: 'test' });

      expect(config.parallel).toBe(true);
      expect(config.timeout).toBe(180000);
      
      // All suites should be enabled with shorter timeouts
      config.testSuites.forEach(suite => {
        expect(suite.enabled).toBe(true);
        expect(suite.timeout).toBeLessThan(60000);
      });
    });

    it('should load configuration from file when configPath provided', async () => {
      const fileConfig = {
        parallel: true,
        timeout: 120000,
        testSuites: [
          {
            name: 'unit',
            type: 'unit' as const,
            enabled: true,
            timeout: 15000,
            retries: 0
          }
        ]
      };

      mockReadFile.mockResolvedValue(JSON.stringify(fileConfig));

      const config = await TestConfigManager.loadConfig({ 
        configPath: './custom-config.json' 
      });

      expect(config.parallel).toBe(true);
      expect(config.timeout).toBe(120000);
      expect(config.testSuites[0].timeout).toBe(15000);
      expect(mockReadFile).toHaveBeenCalledWith('./custom-config.json', 'utf8');
    });

    it('should handle file loading errors gracefully', async () => {
      mockReadFile.mockRejectedValue(new Error('File not found'));

      // Should not throw and should return default config
      const config = await TestConfigManager.loadConfig({ 
        configPath: './non-existent.json' 
      });

      expect(config).toHaveProperty('testSuites');
      expect(config.testSuites).toHaveLength(3);
    });

    it('should apply user overrides on top of other configurations', async () => {
      const overrides = {
        parallel: true,
        outputDir: './custom-output',
        testSuites: [
          {
            name: 'unit',
            type: 'unit' as const,
            enabled: false,
            timeout: 10000,
            retries: 5
          }
        ]
      };

      const config = await TestConfigManager.loadConfig({ 
        environment: 'development',
        overrides 
      });

      expect(config.parallel).toBe(true); // Override applied
      expect(config.outputDir).toBe('./custom-output'); // Override applied
      
      const unitSuite = config.testSuites.find(suite => suite.name === 'unit');
      expect(unitSuite?.enabled).toBe(false); // Override applied
      expect(unitSuite?.timeout).toBe(10000); // Override applied
      expect(unitSuite?.retries).toBe(5); // Override applied
    });
  });

  describe('saveConfig', () => {
    it('should save configuration to specified file', async () => {
      const config: TestOrchestratorConfig = {
        testSuites: [
          {
            name: 'unit',
            type: 'unit',
            enabled: true,
            timeout: 30000,
            retries: 0
          }
        ],
        parallel: false,
        timeout: 300000
      };

      mockWriteFile.mockResolvedValue(undefined);

      await TestConfigManager.saveConfig(config, './test-config.json');

      expect(mockWriteFile).toHaveBeenCalledWith(
        './test-config.json',
        JSON.stringify(config, null, 2),
        'utf8'
      );
    });
  });

  describe('createDefaultConfig', () => {
    it('should create default configuration file', async () => {
      mockWriteFile.mockResolvedValue(undefined);

      await TestConfigManager.createDefaultConfig('./default-config.json');

      expect(mockWriteFile).toHaveBeenCalledWith(
        './default-config.json',
        expect.stringContaining('"testSuites"'),
        'utf8'
      );
    });
  });

  describe('getTestSuiteConfig', () => {
    it('should return configuration for specified test suite', async () => {
      const config = await TestConfigManager.loadConfig();
      const unitConfig = TestConfigManager.getTestSuiteConfig(config, 'unit');

      expect(unitConfig).toBeDefined();
      expect(unitConfig?.name).toBe('unit');
      expect(unitConfig?.type).toBe('unit');
    });

    it('should return undefined for non-existent test suite', async () => {
      const config = await TestConfigManager.loadConfig();
      const nonExistentConfig = TestConfigManager.getTestSuiteConfig(config, 'non-existent');

      expect(nonExistentConfig).toBeUndefined();
    });
  });

  describe('updateTestSuiteConfig', () => {
    it('should update configuration for specified test suite', async () => {
      const config = await TestConfigManager.loadConfig();
      const updatedConfig = TestConfigManager.updateTestSuiteConfig(config, 'unit', {
        timeout: 15000,
        retries: 3
      });

      const unitConfig = TestConfigManager.getTestSuiteConfig(updatedConfig, 'unit');
      expect(unitConfig?.timeout).toBe(15000);
      expect(unitConfig?.retries).toBe(3);
      expect(unitConfig?.name).toBe('unit'); // Other properties preserved
      expect(unitConfig?.type).toBe('unit');
    });

    it('should not modify original configuration', async () => {
      const config = await TestConfigManager.loadConfig();
      const originalUnitConfig = TestConfigManager.getTestSuiteConfig(config, 'unit');
      const originalTimeout = originalUnitConfig?.timeout;

      TestConfigManager.updateTestSuiteConfig(config, 'unit', {
        timeout: 99999
      });

      const stillOriginalConfig = TestConfigManager.getTestSuiteConfig(config, 'unit');
      expect(stillOriginalConfig?.timeout).toBe(originalTimeout);
    });
  });

  describe('toggleTestSuite', () => {
    it('should enable test suite', async () => {
      const config = await TestConfigManager.loadConfig({ environment: 'development' });
      
      // E2E is disabled in development by default
      const e2eConfig = TestConfigManager.getTestSuiteConfig(config, 'e2e');
      expect(e2eConfig?.enabled).toBe(false);

      const updatedConfig = TestConfigManager.toggleTestSuite(config, 'e2e', true);
      const updatedE2eConfig = TestConfigManager.getTestSuiteConfig(updatedConfig, 'e2e');
      
      expect(updatedE2eConfig?.enabled).toBe(true);
    });

    it('should disable test suite', async () => {
      const config = await TestConfigManager.loadConfig();
      const updatedConfig = TestConfigManager.toggleTestSuite(config, 'unit', false);
      const unitConfig = TestConfigManager.getTestSuiteConfig(updatedConfig, 'unit');
      
      expect(unitConfig?.enabled).toBe(false);
    });
  });

  describe('getEnabledTestSuites', () => {
    it('should return only enabled test suites', async () => {
      const config = await TestConfigManager.loadConfig({ environment: 'development' });
      const enabledSuites = TestConfigManager.getEnabledTestSuites(config);

      expect(enabledSuites).toHaveLength(1); // Only unit tests enabled in dev
      expect(enabledSuites[0].name).toBe('unit');
    });

    it('should return all suites when all are enabled', async () => {
      const config = await TestConfigManager.loadConfig({ environment: 'production' });
      const enabledSuites = TestConfigManager.getEnabledTestSuites(config);

      expect(enabledSuites).toHaveLength(3);
      expect(enabledSuites.map(suite => suite.name)).toEqual(['unit', 'integration', 'e2e']);
    });
  });

  describe('configuration validation', () => {
    it('should validate test suite names', async () => {
      const invalidConfig = {
        testSuites: [
          {
            name: '', // Invalid empty name
            type: 'unit' as const,
            enabled: true,
            timeout: 30000,
            retries: 0
          }
        ]
      };

      await expect(
        TestConfigManager.loadConfig({ overrides: invalidConfig })
      ).rejects.toThrow('Test suite name is required');
    });

    it('should validate test suite types', async () => {
      const invalidConfig = {
        testSuites: [
          {
            name: 'invalid',
            type: 'invalid-type' as any,
            enabled: true,
            timeout: 30000,
            retries: 0
          }
        ]
      };

      await expect(
        TestConfigManager.loadConfig({ overrides: invalidConfig })
      ).rejects.toThrow('Invalid test suite type: invalid-type');
    });

    it('should validate timeout values', async () => {
      const invalidConfig = {
        testSuites: [
          {
            name: 'unit',
            type: 'unit' as const,
            enabled: true,
            timeout: -1000, // Invalid negative timeout
            retries: 0
          }
        ]
      };

      await expect(
        TestConfigManager.loadConfig({ overrides: invalidConfig })
      ).rejects.toThrow('Invalid timeout for suite unit: -1000');
    });

    it('should validate retry values', async () => {
      const invalidConfig = {
        testSuites: [
          {
            name: 'unit',
            type: 'unit' as const,
            enabled: true,
            timeout: 30000,
            retries: -1 // Invalid negative retries
          }
        ]
      };

      await expect(
        TestConfigManager.loadConfig({ overrides: invalidConfig })
      ).rejects.toThrow('Invalid retries for suite unit: -1');
    });

    it('should validate overall timeout', async () => {
      const invalidConfig = {
        timeout: -5000 // Invalid negative timeout
      };

      await expect(
        TestConfigManager.loadConfig({ overrides: invalidConfig })
      ).rejects.toThrow('Invalid overall timeout: -5000');
    });

    it('should normalize output directory paths', async () => {
      const config = await TestConfigManager.loadConfig({
        overrides: { outputDir: 'test-results' }
      });

      expect(config.outputDir).toBe('./test-results');
    });
  });

  describe('getConfigSchema', () => {
    it('should return valid JSON schema', () => {
      const schema = TestConfigManager.getConfigSchema();

      expect(schema).toHaveProperty('type', 'object');
      expect(schema).toHaveProperty('properties');
      expect(schema).toHaveProperty('required');
      
      const properties = (schema as any).properties;
      expect(properties).toHaveProperty('testSuites');
      expect(properties.testSuites.type).toBe('array');
    });
  });
});