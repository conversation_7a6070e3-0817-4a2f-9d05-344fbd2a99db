#!/usr/bin/env node

/**
 * Demo script to showcase the Test Orchestrator Framework
 * This demonstrates the key functionality implemented in task 3
 */

import { TestOrchestrator } from './test-orchestrator.js';
import { TestConfigManager } from './test-config.js';

async function demonstrateTestOrchestrator() {
  console.log('🚀 Test Orchestrator Framework Demo\n');

  try {
    // 1. Load configuration for different environments
    console.log('📋 Loading test configurations...');
    
    const devConfig = await TestConfigManager.loadConfig({ 
      environment: 'development' 
    });
    console.log(`Development config: ${devConfig.testSuites.filter(s => s.enabled).length} enabled suites`);

    const prodConfig = await TestConfigManager.loadConfig({ 
      environment: 'production' 
    });
    console.log(`Production config: ${prodConfig.testSuites.filter(s => s.enabled).length} enabled suites`);

    // 2. Create test orchestrator with development config (faster for demo)
    console.log('\n🎭 Creating Test Orchestrator...');
    const orchestrator = new TestOrchestrator(devConfig);

    // 3. Show configuration management capabilities
    console.log('\n⚙️  Configuration Management Demo:');
    
    // Get specific test suite config
    const unitConfig = TestConfigManager.getTestSuiteConfig(devConfig, 'unit');
    console.log(`Unit test config: timeout=${unitConfig?.timeout}ms, retries=${unitConfig?.retries}`);

    // Toggle test suite
    const modifiedConfig = TestConfigManager.toggleTestSuite(devConfig, 'integration', true);
    const enabledSuites = TestConfigManager.getEnabledTestSuites(modifiedConfig);
    console.log(`After enabling integration: ${enabledSuites.length} enabled suites`);

    // 4. Execute tests (this will run actual Jest tests)
    console.log('\n🧪 Executing Test Suites...');
    console.log('Note: This will run actual Jest tests, which may take some time...\n');

    const startTime = Date.now();
    const results = await orchestrator.executeAll({ verbose: true });
    const executionTime = Date.now() - startTime;

    // 5. Show test result aggregation
    console.log('\n📊 Test Results Aggregation:');
    const aggregated = orchestrator.aggregateResults(results);
    
    console.log(`Total Tests: ${aggregated.totalTests}`);
    console.log(`Passed: ${aggregated.totalPassed}`);
    console.log(`Failed: ${aggregated.totalFailed}`);
    console.log(`Duration: ${executionTime}ms`);
    console.log(`Overall Status: ${aggregated.overallPassed ? '✅ PASSED' : '❌ FAILED'}`);

    // 6. Show failure reporting
    if (aggregated.totalFailed > 0) {
      console.log('\n🚨 Failure Details:');
      Object.entries(aggregated.failuresByType).forEach(([type, failures]) => {
        console.log(`\n${type.toUpperCase()} Test Failures:`);
        failures.slice(0, 3).forEach(failure => { // Show first 3 failures
          console.log(`  - ${failure.testName}`);
          console.log(`    Error: ${failure.error.split('\n')[0]}`); // First line of error
        });
      });
    }

    // 7. Generate comprehensive report
    console.log('\n📄 Generating Test Report...');
    const reportJson = await orchestrator.generateReport(results);
    const report = JSON.parse(reportJson);
    
    console.log(`Report generated with ${report.results.length} test suite results`);
    console.log(`Report timestamp: ${report.timestamp}`);

    // 8. Show individual test suite results
    console.log('\n📋 Individual Test Suite Results:');
    results.forEach((result, index) => {
      const suiteName = devConfig.testSuites[index]?.name || `Suite ${index}`;
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${suiteName}: ${result.testCount} tests, ${result.failures.length} failures, ${result.duration}ms`);
    });

    console.log('\n🎉 Test Orchestrator Framework Demo Complete!');
    console.log('\nKey Features Demonstrated:');
    console.log('✅ Test suite runner that executes Jest and Playwright tests');
    console.log('✅ Test result aggregation and failure reporting');
    console.log('✅ Test configuration management system');
    console.log('✅ Comprehensive unit test coverage');

  } catch (error) {
    console.error('❌ Demo failed:', error);
    process.exit(1);
  }
}

// Run the demo if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  demonstrateTestOrchestrator().catch(console.error);
}

export { demonstrateTestOrchestrator };