
//#Website Navigation
//#Hero
//#About
//#Services
//#Products
//#Brands
//#Footer

import { Shield, Zap, Star, Award, Settings, Scissors, RotateCw, Thermometer } from 'lucide-react';
import Heading from './Heading';

const productCategories = [
  {
    icon: Shield,
    title: "Protective Gear",
    description: "The Ice Box offers premium helmets, pads, and protective equipment from top brands to keep you safe on the ice.",
    items: ["Helmets & Cages", "Shoulder Pads", "Elbow Pads", "Shin Guards", "Protective Pants"]
  },
  {
    icon: Zap,
    title: "Sticks & Equipment",
    description: "Discover The Ice Box's selection of high-performance sticks and essential playing equipment for players at every level.",
    items: ["Hockey Sticks", "Gloves", "Skates", "Equipment Bags", "Training Aids"]
  },
  {
    icon: Star,
    title: "Apparel & Accessories",
    description: "Complete your look with The Ice Box's premium team jerseys, practice wear, and hockey lifestyle gear.",
    items: ["Jerseys & Socks", "Practice Jerseys", "Base Layers", "Casual Wear", "Accessories"]
  },
  {
    icon: Award,
    title: "Goalie Specialized",
    description: "The Ice Box specializes in complete goaltender equipment and specialized gear for the last line of defense.",
    items: ["Goalie Masks", "Chest & Arm", "Leg Pads", "Goalie Sticks", "Specialized Gear"]
  }
];

const serviceCategories = [
  {
    icon: Zap,
    title: "Skate Sharpening",
    description: "Precision sharpening services to match your playing style and ice conditions.",
    items: ["Standard Sharpening", "Cross-Grinding", "Radius of Hollow (ROH) Options", "Quick Turnaround"]
  },
  {
    icon: Settings,
    title: "Blade Profiling",
    description: "Custom contouring to optimize stride, agility, and performance on the ice.",
    items: ["Custom Profiling", "Performance Analysis", "Player-Specific Adjustments", "Blade Balancing", "Multi-Radius Options"]
  },
  {
    icon: Scissors,
    title: "Blade & Holder Services",
    description: "Expert blade and holder replacement to extend the life of your skates.",
    items: ["Blade Replacement", "TUUK Holder Installation", "CCM XS Holder Setup", "Runner Replacement", "Full Blade Assembly"]
  },
  {
    icon: Shield,
    title: "Eyelet Repair",
    description: "Professional repair and replacement of damaged or pulled-out lace eyelets.",
    items: ["Eyelet Replacement", "Riveting Services", "Broken Eyelet Repair", "Reinforcement", "Speed Lace Installation"]
  },
  {
    icon: RotateCw,
    title: "Holder Replacement",
    description: "Complete holder replacement services for all major skate brands and models.",
    items: ["Holder Removal", "Alignment Check", "Custom Mounting", "Blade Compatibility Check", "Professional Installation"]
  },
  {
    icon: Thermometer,
    title: "Heat Molding",
    description: "Custom skate fitting through heat molding for comfort and performance.",
    items: ["Oven Baking", "Custom Fitting", "Punch Outs", "Stretching", "Break-In Assistance"]
  }
];

const Products = () => {
  return (
    <section className="py-20 text-white" style={{ backgroundColor: '#1b263b' }}>
      <div className="container mx-auto px-8">
        <div className="text-center mb-16">
          <Heading as="h2" className="mb-6">
            Equipment We Offer
          </Heading>
          <p className="text-xl text-white max-w-3xl mx-auto opacity-90 mb-12">
            At The Ice Box, we're proud to stock the finest hockey equipment from the world's leading brands and provide expert skate services to keep you performing at your best.
          </p>
          
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6 mb-16">
          {productCategories.map((category, index) => (
            <div 
              key={`product-${index}`}
              className="bg-white/10 backdrop-blur-sm p-8 rounded-xl border border-white/20 hover:border-blue-300/50 transition-all duration-300 group"
            >
              <div className="flex items-start gap-6">
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                  <category.icon className="h-8 w-8 text-white" />
                </div>
                
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-3">
                    {category.title}
                  </h3>
                  
                  <p className="text-white mb-6 opacity-90">
                    {category.description}
                  </p>
                  
                  <ul className="space-y-2">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center gap-2 text-white">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center mb-12">
          <Heading as="h3" className="text-4xl md:text-5xl font-bold text-white mb-6">Skate Services</Heading>
          <p className="text-xl text-white max-w-3xl mx-auto opacity-90 mb-8">
            Our certified technicians use state-of-the-art equipment to ensure precision and quality in every service.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {serviceCategories.map((category, index) => (
            <div 
              key={`service-${index}`}
              className="bg-white/10 backdrop-blur-sm p-8 rounded-xl border border-white/20 hover:border-blue-300/50 transition-all duration-300 group"
            >
              <div className="flex items-start gap-6">
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                  <category.icon className="h-8 w-8 text-white" />
                </div>
                
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-3">
                    {category.title}
                  </h3>
                  
                  <p className="text-white mb-6 opacity-90">
                    {category.description}
                  </p>
                  
                  <ul className="space-y-2">
                    {category.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center gap-2 text-white">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
        

      </div>
    </section>
  );
};

export default Products;
