import React from 'react';
import { Link } from 'react-router-dom';
import Menu from './Menu';
import PWAInstallButton from './PWAInstallButton';

const Header: React.FC = () => {
  return (
    <header className="fixed top-0 left-0 w-full z-30 bg-[#1b263be6] backdrop-blur-md border-b border-white/10 flex items-center justify-between px-8 py-3">
      <div className="flex items-center">
        <Link to="/">
          <img src="/Icebox.webp" alt="The Ice Box Logo" className="h-20 w-auto" />
        </Link>
      </div>
      <div className="flex items-center gap-4">
        <PWAInstallButton 
          variant="outline" 
          size="sm" 
          className="hidden sm:flex text-white border-white/20 hover:bg-white/10"
        />
        <Menu />
      </div>
    </header>
  );
};

export default Header;