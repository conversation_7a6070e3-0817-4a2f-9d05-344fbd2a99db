import React from 'react';
import { Product } from '../types/product';

interface ProductCardProps {
  product: Product;
}

export const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  return (
    <div className="bg-white rounded-lg shadow p-4 flex flex-col items-center hover:shadow-lg transition-shadow border border-gray-100">
      <img
        src={product.image}
        alt={product.name}
        className="w-32 h-32 object-contain mb-4"
      />
      <div className="font-semibold text-lg text-gray-900 mb-1 text-center">{product.name}</div>
      <div className="text-blue-700 font-bold text-base mb-2">{product.price}</div>
      {product.description && (
        <p className="text-xs text-gray-500 mt-2 text-center">{product.description}</p>
      )}
    </div>
  );
};