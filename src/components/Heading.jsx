import React from 'react';
import PropTypes from 'prop-types';

const Heading = ({ 
  children, 
  as: Tag = 'h2', 
  className = '', 
  ...props 
}) => {
  return (
    <Tag 
      className={`text-4xl md:text-5xl font-bold text-white mb-6 ${className}`}
      {...props}
    >
      {children}
    </Tag>
  );
};

Heading.propTypes = {
  /** The heading content */
  children: PropTypes.node.isRequired,
  /** The HTML element to render (h1, h2, h3, etc.) */
  as: PropTypes.oneOf(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']),
  /** Additional CSS classes */
  className: PropTypes.string,
};

export default Heading;
