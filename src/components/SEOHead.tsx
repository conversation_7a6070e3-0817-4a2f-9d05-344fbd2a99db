import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  structuredData?: object;
}

const SEOHead = ({
  title = 'The Ice Box - Premier Hockey Equipment Store | Harbor City, Los Angeles, CA',
  description = 'Professional hockey equipment store serving Harbor City, Los Angeles, and Southern California. Expert fitting services, premium gear from top brands. Located inside Skating Edge Arena. Opening August 2025.',
  keywords = [
    'hockey equipment Harbor City',
    'hockey equipment Los Angeles',
    'hockey equipment Southern California',
    'hockey equipment So Cal',
    'ice hockey gear Harbor City',
    'hockey store Los Angeles',
    'hockey shop Southern California',
    'professional hockey fitting Harbor City',
    'Bauer CCM Warrior Los Angeles',
    'skate sharpening Harbor City',
    'hockey equipment California',
    'Bay Harbor Red Wings',
    'elite hockey gear Los Angeles',
    'hockey store near me',
    'Torrance hockey equipment',
    'San Pedro hockey gear',
    'Wilmington hockey store',
    'Carson hockey equipment',
    'Long Beach hockey gear'
  ],
  image = '/og-image.jpg',
  url = 'https://iceboxhockey.com',
  type = 'website',
  structuredData
}: SEOHeadProps) => {
  const fullTitle = title.includes('Ice Box') ? title : `${title} | The Ice Box`;
  
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "SportsStore",
    "name": "The Ice Box Hockey Equipment Store",
    "description": description,
    "url": url,
    "logo": "https://iceboxhockey.com/Icebox.webp",
    "image": `https://iceboxhockey.com${image}`,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "23770 S Western Ave",
      "addressLocality": "Harbor City",
      "addressRegion": "CA",
      "postalCode": "90710",
      "addressCountry": "US"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "33.7969",
      "longitude": "-118.3001"
    },
    "telephone": "******-ICE-PUCK",
    "email": "<EMAIL>",
    "openingHours": [
      "Mo-Fr 10:00-20:00",
      "Sa 10:00-20:00", 
      "Su 12:00-18:00"
    ],
    "priceRange": "$$",
    "paymentAccepted": ["Cash", "Credit Card"],
    "currenciesAccepted": "USD",
    "areaServed": [
      {
        "@type": "City",
        "name": "Harbor City",
        "addressRegion": "CA",
        "addressCountry": "US"
      },
      {
        "@type": "City",
        "name": "Los Angeles",
        "addressRegion": "CA",
        "addressCountry": "US"
      },
      {
        "@type": "State",
        "name": "Southern California",
        "addressRegion": "CA",
        "addressCountry": "US"
      },
      {
        "@type": "City",
        "name": "Torrance",
        "addressRegion": "CA",
        "addressCountry": "US"
      },
      {
        "@type": "City",
        "name": "San Pedro",
        "addressRegion": "CA",
        "addressCountry": "US"
      },
      {
        "@type": "City",
        "name": "Wilmington",
        "addressRegion": "CA",
        "addressCountry": "US"
      },
      {
        "@type": "City",
        "name": "Carson",
        "addressRegion": "CA",
        "addressCountry": "US"
      },
      {
        "@type": "City",
        "name": "Long Beach",
        "addressRegion": "CA",
        "addressCountry": "US"
      }
    ],
    "sameAs": [
      "https://www.facebook.com/people/Ice-Box-Hockey-Shop/61577383866320/",
      "https://www.instagram.com/iceboxhockey_ca/"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Hockey Equipment",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Product",
            "name": "Professional Hockey Equipment Fitting"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Product",
            "name": "Elite Skate Sharpening Services"
          }
        }
      ]
    }
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content="The Ice Box Hockey Team" />
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="googlebot" content="index, follow" />
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={`https://iceboxhockey.com${image}`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="The Ice Box Hockey" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={`https://iceboxhockey.com${image}`} />
      <meta name="twitter:site" content="@iceboxhockey" />
      
      {/* Additional SEO */}
      <meta name="theme-color" content="#1b263b" />
      <meta name="msapplication-TileColor" content="#1b263b" />
      <link rel="canonical" href={url} />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData || defaultStructuredData)}
      </script>
    </Helmet>
  );
};

export default SEOHead;