
import React from 'react';
import { Link } from 'react-router-dom';
import OptimizedImage from './OptimizedImage';
import { trackBrandClick } from '@/utils/analytics';

const brands = [
  {
    name: '<PERSON>',
    title: 'Bauer Hockey Equipment',
    alt: 'Bauer Hockey Logo',
    src: '/Bauer_4.webp',
    width: 160,
    height: 80,
    website: 'https://www.bauer.com/'
  },
  {
    name: 'CC<PERSON>',
    title: 'CCM Hockey Equipment',
    alt: 'CCM Hockey Logo',
    src: '/CCM-Logo_2.webp',
    width: 160,
    height: 80,
    website: 'https://www.ccmhockey.com/'
  },
  {
    name: '<PERSON> Temper',
    title: 'True Temper Hockey & Baseball',
    alt: 'True Temper Logo',
    src: '/True_Temper_Hockey-Baseball_CatIcon.webp',
    width: 160,
    height: 80,
    website: 'https://www.truetempersports.com/'
  },
  {
    name: 'Warrior',
    title: 'Warrior Hockey Equipment',
    alt: 'Warrior Hockey Logo',
    src: '/warrior.webp',
    width: 160,
    height: 80,
    website: 'https://www.warrior.com/hockey'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    title: '<PERSON>nwell Hockey Equipment',
    alt: 'Winnwell Logo',
    src: '/<PERSON>nwell-Brand-Icon_1.webp',
    width: 160,
    height: 80,
    website: 'https://winnwell.ca/'
  },
  {
    name: 'Biosteel',
    title: 'Biosteel Sports Nutrition',
    alt: 'Biosteel Logo',
    src: '/Biosteel_1.webp',
    width: 160,
    height: 80,
    website: 'https://biosteel.com/'
  },
  {
    name: 'Howies',
    title: 'Howies Hockey Tape',
    alt: 'Howies Hockey Tape Logo',
    src: '/Howies_Hockey_Tape_Category_Icon_1.webp',
    width: 160,
    height: 80,
    website: 'https://howieshockeytape.com/'
  },
  {
    name: 'Marsblade',
    title: 'Marsblade Hockey Skates',
    alt: 'Marsblade Logo',
    src: '/Marsblade_1.webp',
    width: 160,
    height: 80,
    website: 'https://www.marsblade.com/'
  },
  {
    name: 'Mission',
    title: 'Mission Hockey Equipment',
    alt: 'Mission Hockey Logo',
    src: '/Mission-Icon-2023_1.webp',
    width: 160,
    height: 80,
    website: 'https://www.bauer.com/'
  },
  {
    name: 'Sherwood',
    title: 'Sherwood Hockey Equipment',
    alt: 'Sher-Wood Logo',
    src: '/SherWood_3_2.webp',
    width: 160,
    height: 80,
    website: 'https://www.sherwoodhockey.com/'
  }
];

const Brands = () => {
  return (
    <section id="brands" className="py-20 text-white" style={{ backgroundColor: '#1b263b' }}>
      <div className="container px-8 mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Brands We Are Proud to Offer</h2>
          <p className="text-xl text-white max-w-3xl mx-auto opacity-90 mb-12">
            We carry all the top hockey brands to meet your equipment needs.
          </p>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
          {brands.map((brand, index) => (
            <div key={index} className="group">
                <Link 
                to={brand.website}
                target="_blank"
                rel="noopener noreferrer"
                className="block"
                onClick={() => trackBrandClick(brand.name, brand.website)}
              >
                <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 h-full flex flex-col items-center justify-center">
                  <div className="relative w-full aspect-square flex items-center justify-center p-4">
                    <OptimizedImage
                      src={brand.src}
                      alt={brand.alt}
                      width={brand.width}
                      height={brand.height}
                      loading="lazy"
                      className="w-full h-auto max-h-[80px] object-contain transition-all duration-300 group-hover:scale-105 grayscale group-hover:grayscale-0"
                    />
                  </div>
                  <span className="mt-2 text-sm font-medium text-gray-700 group-hover:text-blue-600 transition-colors">
                    {brand.name}
                  </span>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Brands;
