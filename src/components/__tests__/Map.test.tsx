import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import Map from '../Map';

// Workaround for older version of testing-library
const screen = {
  getByRole: (role: string) => document.querySelector(`[role="${role}"]`),
  queryByRole: (role: string) => document.querySelector(`[role="${role}"]`),
  getByText: (text: string | RegExp) => {
    const elements = Array.from(document.querySelectorAll('*'));
    return elements.find(el => {
      const content = el.textContent || '';
      return typeof text === 'string' 
        ? content.includes(text)
        : text.test(content);
    });
  }
};

const waitFor = async (callback: () => void, { timeout = 1000 } = {}) => {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    try {
      callback();
      return;
    } catch (err) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  callback();
};

// Define types for Google Maps API
type GeocoderStatus = 'OK' | 'ZERO_RESULTS' | 'ERROR' | 'INVALID_REQUEST' | 'OVER_QUERY_LIMIT' | 'REQUEST_DENIED' | 'UNKNOWN_ERROR';

interface LatLng {
  lat: number;
  lng: number;
  toJSON: () => { lat: number; lng: number };
  toString: () => string;
  toUrlValue: (precision?: number) => string;
}

// Mock the Google Maps API
const mockGeocoder = {
  geocode: jest.fn(),
};

const mockMarker = {
  setMap: jest.fn(),
  setPosition: jest.fn(),
};

const mockMap = {
  setCenter: jest.fn(),
  setZoom: jest.fn(),
};

// Mock the global google object
const mockGoogleMaps = {
  maps: {
    Map: jest.fn().mockImplementation(() => mockMap),
    Marker: jest.fn().mockImplementation(() => mockMarker),
    Geocoder: jest.fn().mockImplementation(() => mockGeocoder),
    GeocoderStatus: {
      OK: 'OK' as GeocoderStatus,
      ZERO_RESULTS: 'ZERO_RESULTS' as GeocoderStatus,
      ERROR: 'ERROR' as GeocoderStatus,
      INVALID_REQUEST: 'INVALID_REQUEST' as GeocoderStatus,
      OVER_QUERY_LIMIT: 'OVER_QUERY_LIMIT' as GeocoderStatus,
      REQUEST_DENIED: 'REQUEST_DENIED' as GeocoderStatus,
      UNKNOWN_ERROR: 'UNKNOWN_ERROR' as GeocoderStatus,
    },
    event: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addDomListener: jest.fn(),
      addDomListenerOnce: jest.fn(),
      addListenerOnce: jest.fn(),
      clearInstanceListeners: jest.fn(),
      clearListeners: jest.fn(),
      trigger: jest.fn(),
    },
    LatLng: class implements LatLng {
      constructor(public lat: number, public lng: number) {}
      toJSON() { return { lat: this.lat, lng: this.lng }; }
      toString() { return `(${this.lat}, ${this.lng})`; }
      toUrlValue() { return `${this.lat},${this.lng}`; }
    },
  },
};

// Mock the global google object with proper type assertion
const globalAny = global as unknown as { google: unknown };
globalAny.google = mockGoogleMaps;

// Setup mock implementations before each test
beforeEach(() => {
  jest.clearAllMocks();
  
  // Reset mock implementations
  mockGeocoder.geocode.mockImplementation((request, callback) => {
    if (request.address === '23770 S Western Ave, Harbor City, CA 90710') {
      callback([{
        geometry: {
          location: {
            lat: () => 33.7866,
            lng: () => -118.2987,
            toJSON: () => ({ lat: 33.7866, lng: -118.2987 }),
            toUrlValue: () => '33.7866,-118.2987',
          },
        },
        formatted_address: '23770 S Western Ave, Harbor City, CA 90710, USA',
      }], 'OK');
    } else {
      callback([], 'ZERO_RESULTS');
    }
  });
});

describe('Map Component', () => {

  it('renders with default props', () => {
    render(<Map />);
    
    // Verify iframe is rendered with default address
    const iframe = document.querySelector('iframe');
    expect(iframe).toBeInTheDocument();
    expect(iframe?.src).toContain(encodeURIComponent('23770 S Western Ave, Harbor City, CA 90710'));
  });

  it('renders with custom address prop', () => {
    const testAddress = '1600 Amphitheatre Parkway, Mountain View, CA 94043';
    render(<Map address={testAddress} />);
    
    // Verify iframe is rendered with custom address
    const iframe = document.querySelector('iframe');
    expect(iframe).toBeInTheDocument();
    expect(iframe?.src).toContain(encodeURIComponent(testAddress));
  });

  it('renders with custom className', () => {
    const customClass = 'custom-map-class';
    render(<Map className={customClass} />);
    
    // Verify custom class is applied
    const mapContainer = document.querySelector('.relative.w-full');
    expect(mapContainer).toHaveClass(customClass);
  });

  it('displays location information overlay', () => {
    render(<Map />);
    
    // Verify location overlay is displayed
    const locationText = screen.getByText('Located Inside the Skating Edge Ice Arena');
    expect(locationText).toBeInTheDocument();
    
    // Verify MapPin icon is present
    const mapIcon = document.querySelector('.lucide-map-pin');
    expect(mapIcon).toBeInTheDocument();
  });

  it('updates iframe src when address prop changes', () => {
    const initialAddress = '23770 S Western Ave, Harbor City, CA 90710';
    const { rerender } = render(<Map address={initialAddress} />);
    
    const newAddress = '1600 Amphitheatre Parkway, Mountain View, CA 94043';
    rerender(<Map address={newAddress} />);
    
    // Verify iframe src is updated with new address
    const iframe = document.querySelector('iframe');
    expect(iframe?.src).toContain(encodeURIComponent(newAddress));
  });
});
