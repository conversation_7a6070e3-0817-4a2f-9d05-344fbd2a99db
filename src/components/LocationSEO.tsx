import React from 'react';
import SEOHead from './SEOHead';

interface LocationSEOProps {
  location: string;
  region: string;
  nearbyAreas?: string[];
}

const LocationSEO: React.FC<LocationSEOProps> = ({ location, region, nearbyAreas = [] }) => {
  const generateLocationKeywords = () => {
    const baseKeywords = [
      `hockey equipment ${location}`,
      `hockey store ${location}`,
      `ice hockey gear ${location}`,
      `hockey shop ${location}`,
      `professional hockey fitting ${location}`,
      `skate sharpening ${location}`,
      `hockey equipment near ${location}`,
      `Bauer CCM Warrior ${location}`,
      `elite hockey gear ${location}`,
      `team hockey sales ${location}`,
      `custom hockey equipment ${location}`,
      `hockey sticks ${location}`,
      `hockey skates ${location}`,
      `protective hockey gear ${location}`
    ];

    // Add nearby areas keywords
    nearbyAreas.forEach(area => {
      baseKeywords.push(
        `hockey equipment ${area}`,
        `hockey store ${area}`,
        `hockey gear ${area}`
      );
    });

    return baseKeywords;
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "@id": "https://iceboxhockey.com",
    "name": "The Ice Box Hockey Equipment Store",
    "description": `Professional hockey equipment store serving ${location}, ${region}. Expert fitting services and premium gear from top brands.`,
    "url": "https://iceboxhockey.com",
    "logo": "https://iceboxhockey.com/Icebox.webp",
    "image": "https://iceboxhockey.com/og-image.jpg",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "23770 S Western Ave",
      "addressLocality": "Harbor City",
      "addressRegion": "CA",
      "postalCode": "90710",
      "addressCountry": "US"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "33.7969",
      "longitude": "-118.3001"
    },
    "telephone": "******-ICE-PUCK",
    "email": "<EMAIL>",
    "openingHours": [
      "Mo-Fr 10:00-20:00",
      "Sa 10:00-20:00", 
      "Su 12:00-18:00"
    ],
    "priceRange": "$$",
    "paymentAccepted": ["Cash", "Credit Card"],
    "currenciesAccepted": "USD",
    "areaServed": {
      "@type": "City",
      "name": location,
      "addressRegion": "CA",
      "addressCountry": "US"
    },
    "sameAs": [
      "https://www.facebook.com/people/Ice-Box-Hockey-Shop/61577383866320/",
      "https://www.instagram.com/iceboxhockey_ca/"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Hockey Equipment",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Product",
            "name": "Professional Hockey Equipment Fitting"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Product",
            "name": "Elite Skate Sharpening Services"
          }
        }
      ]
    }
  };

  return (
    <SEOHead
      title={`The Ice Box - Premier Hockey Equipment Store | ${location}, ${region}`}
      description={`Professional hockey equipment store serving ${location}, ${region}. Expert fitting services, premium gear from top brands including Bauer, CCM, and Warrior. Located inside Skating Edge Arena.`}
      keywords={generateLocationKeywords()}
      type="website"
      url="https://iceboxhockey.com"
      image="/og-image.jpg"
      structuredData={structuredData}
    />
  );
};

export default LocationSEO;