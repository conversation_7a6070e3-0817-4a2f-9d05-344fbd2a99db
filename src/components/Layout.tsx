import React, { useEffect } from 'react';
import Header from './Header';
import Footer from './Footer';
import PWAStatus from './PWAStatus';
import { useLocation } from 'react-router-dom';
import { useAnalytics } from '@/hooks/useAnalytics';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  
  // Initialize analytics tracking
  useAnalytics();

  useEffect(() => {
    if (location.hash) {
      const id = location.hash.substring(1);
      setTimeout(() => {
        const element = document.getElementById(id);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 0);
    }
  }, [location]);

  return (
    <>
      <Header />
      <main className={location.pathname === '/' ? '' : 'pt-28'}>{children}</main>
      <Footer />
      <PWAStatus />
    </>
  );
};

export default Layout;