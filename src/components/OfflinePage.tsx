import React from 'react';
import { WifiOff, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

interface OfflinePageProps {
  onRetry?: () => void;
}

export const OfflinePage: React.FC<OfflinePageProps> = ({ onRetry }) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* Offline Icon */}
        <div className="mx-auto w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mb-6">
          <WifiOff className="w-12 h-12 text-gray-500" />
        </div>
        
        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          You're Offline
        </h1>
        
        {/* Description */}
        <p className="text-gray-600 mb-8 leading-relaxed">
          It looks like you've lost your internet connection. Don't worry - you can still browse 
          previously visited pages and view cached content.
        </p>
        
        {/* Ice Box Branding */}
        <div className="mb-8">
          <img 
            src="/Icebox.webp" 
            alt="The Ice Box Logo" 
            className="h-16 w-auto mx-auto mb-4"
          />
          <p className="text-sm text-gray-500">
            The Ice Box Hockey - Always here for you
          </p>
        </div>
        
        {/* Action Buttons */}
        <div className="space-y-4">
          <Button 
            onClick={handleRetry}
            className="w-full flex items-center justify-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Try Again
          </Button>
          
          <Link to="/" className="block">
            <Button 
              variant="outline" 
              className="w-full flex items-center justify-center gap-2"
            >
              <Home className="w-4 h-4" />
              Go to Homepage
            </Button>
          </Link>
        </div>
        
        {/* Offline Features */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">
            Available Offline:
          </h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Browse previously visited pages</li>
            <li>• View cached product information</li>
            <li>• Access store contact details</li>
            <li>• Read about our services</li>
          </ul>
        </div>
        
        {/* Network Status */}
        <div className="mt-6 text-xs text-gray-400">
          <p>Your data will sync when you're back online</p>
        </div>
      </div>
    </div>
  );
};

export default OfflinePage;