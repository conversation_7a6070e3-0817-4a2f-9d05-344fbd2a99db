import React from 'react';
import { Wifi, WifiOff, Download, RefreshCw, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { usePWA } from '@/hooks/usePWA';
import { useToast } from '@/hooks/use-toast';

export const PWAStatus: React.FC = () => {
  const { 
    isOnline, 
    updateAvailable, 
    networkType, 
    canInstall, 
    install, 
    refresh, 
    dismissUpdate 
  } = usePWA();
  const { toast } = useToast();

  const handleInstall = async () => {
    const success = await install();
    if (success) {
      toast({
        title: "App Installed!",
        description: "Ice Box Hockey has been added to your home screen.",
      });
    } else {
      toast({
        title: "Installation Failed",
        description: "Unable to install the app. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleUpdate = () => {
    toast({
      title: "Updating App",
      description: "The app will reload with the latest version.",
    });
    setTimeout(refresh, 1000);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-2">
      {/* Update Available Notification */}
      {updateAvailable && (
        <div className="bg-blue-600 text-white p-4 rounded-lg shadow-lg max-w-sm animate-in slide-in-from-bottom-2">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="font-semibold mb-1">Update Available</h4>
              <p className="text-sm text-blue-100 mb-3">
                A new version of the app is ready to install with improved features and performance.
              </p>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleUpdate}
                  className="bg-white text-blue-600 hover:bg-blue-50"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Update Now
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={dismissUpdate}
                  className="text-white hover:bg-blue-700"
                >
                  Later
                </Button>
              </div>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={dismissUpdate}
              className="text-white hover:bg-blue-700 p-1"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Install App Notification */}
      {canInstall && (
        <div className="bg-green-600 text-white p-4 rounded-lg shadow-lg max-w-sm animate-in slide-in-from-bottom-2">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="font-semibold mb-1">Install Ice Box Hockey</h4>
              <p className="text-sm text-green-100 mb-3">
                Get faster access and offline browsing by installing our app on your device.
              </p>
              <Button
                size="sm"
                onClick={handleInstall}
                className="bg-white text-green-600 hover:bg-green-50"
              >
                <Download className="w-4 h-4 mr-1" />
                Install App
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Network Status (only show when offline) */}
      {!isOnline && (
        <div className="bg-orange-600 text-white p-3 rounded-lg shadow-lg max-w-sm animate-in slide-in-from-bottom-2">
          <div className="flex items-center gap-2">
            <WifiOff className="w-5 h-5" />
            <div>
              <p className="font-semibold text-sm">You're Offline</p>
              <p className="text-xs text-orange-100">
                Browsing cached content
              </p>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default PWAStatus;