import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { trackEvent } from '@/utils/analytics';

// User preferences interface
interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'es';
  currency: 'USD' | 'CAD';
  measurementUnit: 'imperial' | 'metric';
  notifications: {
    marketing: boolean;
    updates: boolean;
    orders: boolean;
  };
}

// Shopping cart interface (for future e-commerce)
interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  category: string;
  brand: string;
}

// User session interface
interface UserSession {
  sessionId: string;
  startTime: number;
  pageViews: number;
  interactions: number;
  referrer: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

// Store state interface
interface StoreState {
  // User preferences
  preferences: UserPreferences;
  setPreferences: (preferences: Partial<UserPreferences>) => void;
  
  // Shopping cart (future)
  cart: CartItem[];
  addToCart: (item: CartItem) => void;
  removeFromCart: (itemId: string) => void;
  updateCartQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  
  // User session
  session: UserSession;
  updateSession: (updates: Partial<UserSession>) => void;
  incrementPageViews: () => void;
  incrementInteractions: () => void;
  
  // UI state
  isMenuOpen: boolean;
  setMenuOpen: (open: boolean) => void;
  
  // Search state
  searchHistory: string[];
  addSearchTerm: (term: string) => void;
  clearSearchHistory: () => void;
  
  // Recently viewed items (future)
  recentlyViewed: string[];
  addRecentlyViewed: (itemId: string) => void;
  
  // Wishlist (future)
  wishlist: string[];
  addToWishlist: (itemId: string) => void;
  removeFromWishlist: (itemId: string) => void;
  
  // Performance metrics
  performanceMetrics: {
    pageLoadTime: number;
    interactionDelay: number;
    errorCount: number;
  };
  updatePerformanceMetrics: (metrics: Partial<StoreState['performanceMetrics']>) => void;
}

// Default values
const defaultPreferences: UserPreferences = {
  theme: 'system',
  language: 'en',
  currency: 'USD',
  measurementUnit: 'imperial',
  notifications: {
    marketing: false,
    updates: true,
    orders: true
  }
};

const createSession = (): UserSession => ({
  sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  startTime: Date.now(),
  pageViews: 1,
  interactions: 0,
  referrer: document.referrer || 'direct',
  utmSource: new URLSearchParams(window.location.search).get('utm_source') || undefined,
  utmMedium: new URLSearchParams(window.location.search).get('utm_medium') || undefined,
  utmCampaign: new URLSearchParams(window.location.search).get('utm_campaign') || undefined
});

// Create the store
export const useStore = create<StoreState>()(
  persist(
    (set, get) => ({
      // User preferences
      preferences: defaultPreferences,
      setPreferences: (newPreferences) => {
        set((state) => ({
          preferences: { ...state.preferences, ...newPreferences }
        }));
        
        // Track preference changes
        trackEvent('preferences_updated', {
          preferences: newPreferences,
          event_category: 'user_preferences'
        });
      },

      // Shopping cart
      cart: [],
      addToCart: (item) => {
        set((state) => {
          const existingItem = state.cart.find(cartItem => cartItem.id === item.id);
          
          if (existingItem) {
            return {
              cart: state.cart.map(cartItem =>
                cartItem.id === item.id
                  ? { ...cartItem, quantity: cartItem.quantity + item.quantity }
                  : cartItem
              )
            };
          }
          
          return { cart: [...state.cart, item] };
        });
        
        trackEvent('add_to_cart', {
          item_id: item.id,
          item_name: item.name,
          item_category: item.category,
          item_brand: item.brand,
          price: item.price,
          quantity: item.quantity,
          value: item.price * item.quantity,
          currency: 'USD'
        });
      },
      
      removeFromCart: (itemId) => {
        const item = get().cart.find(cartItem => cartItem.id === itemId);
        
        set((state) => ({
          cart: state.cart.filter(cartItem => cartItem.id !== itemId)
        }));
        
        if (item) {
          trackEvent('remove_from_cart', {
            item_id: itemId,
            item_name: item.name,
            value: item.price * item.quantity,
            currency: 'USD'
          });
        }
      },
      
      updateCartQuantity: (itemId, quantity) => {
        if (quantity <= 0) {
          get().removeFromCart(itemId);
          return;
        }
        
        set((state) => ({
          cart: state.cart.map(item =>
            item.id === itemId ? { ...item, quantity } : item
          )
        }));
      },
      
      clearCart: () => {
        const cartValue = get().cart.reduce((total, item) => total + (item.price * item.quantity), 0);
        
        set({ cart: [] });
        
        trackEvent('cart_cleared', {
          cart_value: cartValue,
          items_count: get().cart.length,
          currency: 'USD'
        });
      },

      // User session
      session: createSession(),
      updateSession: (updates) => {
        set((state) => ({
          session: { ...state.session, ...updates }
        }));
      },
      
      incrementPageViews: () => {
        set((state) => ({
          session: { ...state.session, pageViews: state.session.pageViews + 1 }
        }));
      },
      
      incrementInteractions: () => {
        set((state) => ({
          session: { ...state.session, interactions: state.session.interactions + 1 }
        }));
      },

      // UI state
      isMenuOpen: false,
      setMenuOpen: (open) => {
        set({ isMenuOpen: open });
        
        if (open) {
          trackEvent('menu_opened', {
            event_category: 'navigation'
          });
        }
      },

      // Search
      searchHistory: [],
      addSearchTerm: (term) => {
        const trimmedTerm = term.trim();
        if (!trimmedTerm) return;
        
        set((state) => ({
          searchHistory: [
            trimmedTerm,
            ...state.searchHistory.filter(t => t !== trimmedTerm)
          ].slice(0, 10) // Keep only last 10 searches
        }));
      },
      
      clearSearchHistory: () => {
        set({ searchHistory: [] });
      },

      // Recently viewed
      recentlyViewed: [],
      addRecentlyViewed: (itemId) => {
        set((state) => ({
          recentlyViewed: [
            itemId,
            ...state.recentlyViewed.filter(id => id !== itemId)
          ].slice(0, 20) // Keep only last 20 items
        }));
      },

      // Wishlist
      wishlist: [],
      addToWishlist: (itemId) => {
        set((state) => ({
          wishlist: state.wishlist.includes(itemId)
            ? state.wishlist
            : [...state.wishlist, itemId]
        }));
        
        trackEvent('add_to_wishlist', {
          item_id: itemId,
          event_category: 'wishlist'
        });
      },
      
      removeFromWishlist: (itemId) => {
        set((state) => ({
          wishlist: state.wishlist.filter(id => id !== itemId)
        }));
        
        trackEvent('remove_from_wishlist', {
          item_id: itemId,
          event_category: 'wishlist'
        });
      },

      // Performance metrics
      performanceMetrics: {
        pageLoadTime: 0,
        interactionDelay: 0,
        errorCount: 0
      },
      
      updatePerformanceMetrics: (metrics) => {
        set((state) => ({
          performanceMetrics: { ...state.performanceMetrics, ...metrics }
        }));
      }
    }),
    {
      name: 'ice-box-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        preferences: state.preferences,
        cart: state.cart,
        searchHistory: state.searchHistory,
        recentlyViewed: state.recentlyViewed,
        wishlist: state.wishlist
      })
    }
  )
);

// Selectors for better performance
export const usePreferences = () => useStore((state) => state.preferences);
export const useCart = () => useStore((state) => state.cart);
export const useSession = () => useStore((state) => state.session);
export const useSearchHistory = () => useStore((state) => state.searchHistory);
export const useWishlist = () => useStore((state) => state.wishlist);
export const usePerformanceMetrics = () => useStore((state) => state.performanceMetrics);

export default useStore;