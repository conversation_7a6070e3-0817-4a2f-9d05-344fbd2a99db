import { createRoot } from 'react-dom/client';
import App from './App.tsx'
import './index.css'

// Load Google Maps API key before the app initializes
import './utils/loadGoogleMaps';

// Initialize Google Analytics
import { initializeAnalytics } from './utils/analytics';

// Initialize PWA functionality
import { registerServiceWorker, initializePWAPrompt, initializeNetworkListener } from './utils/pwa';

// Initialize analytics on app startup
initializeAnalytics();

// Initialize PWA features
if (typeof window !== 'undefined') {
  // Register service worker
  registerServiceWorker().catch(console.error);
  
  // Initialize PWA install prompt
  initializePWAPrompt();
  
  // Initialize network status listener
  initializeNetworkListener();
}

createRoot(document.getElementById("root")!).render(<App />);
