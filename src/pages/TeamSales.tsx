import React, { useMemo } from 'react';
import OptimizedImage from '@/components/OptimizedImage';
import SEOHead from '@/components/SEOHead';
const products = [
  {
    name: "Bay Harbor Red Wings Team Hockey Socks",
    price: "$22.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Team socks are designed to stay in place and retain their shape, so players can focus on the game. "
  },
  {
    name: "Bay Harbor Red Wings Jetspeed FT880 Team Hockey Gloves",
    price: "$159.99",
    image: "/bay-harbor_copy_1_.png",
    description: "The CCM Jetspeed FT880 gloves offer high-level protection, comfort, and mobility. They feature a game-ready construction for a better fit, a reinforced Sensalast palm for durability and grip, a soft liner with PU foam for comfort and moisture control, PE foam with inserts for lightweight protection, and a short, pre-angled open cuff for wrist flexibility and freedom of movement."
  },
  {
    name: "Bay Harbor Red Wings Number Kit",
    price: "$9.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Add your number to your hockey helmet. Select your team colors and enter your number to order hockey helmet number stickers. Each hockey number sticker is die-cut printed on a premium 3mm vinyl with adhesive backing that will conform to your helmet. All number decals have a thin white border around the number to pop on any color helmet. "
  },
  {
    name: "Bay Harbor Red Wings Baseball Cap",
    price: "$22.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Elevate your team spirit with our custom embroidered perforated cap. Imprinted with the Bay Harbor logo. The unique perforated fabric ensures a cool, stylish experience on hot days. "
  },
  {
    name: "Bay Harbor Red Wings Team Winter  Jacket",
    price: "$85.99",
    image: "/bay-harbor_copy_1_.png",
    description: "The Winter Team Jacket is perfect for bone-chilling rinks and colder environments. Built with Tactical Degree, this Winter Jacket excels at trapping heat to keep you warm while letting sweat escape in order to keep you dry. For nastier weather, a zip-off hood provides added coverage and insulation."
  },
  {
    name: "Bay Harbor Red Wings Scarf",
    price: "$14.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Stay warm and show your team pride with this cozy Bay Harbor Red Wings scarf. Made from soft, durable materials perfect for cold rink environments and outdoor games."
  },

  {
    name: "Bay Harbor Red Wings Game Jersey",
    price: "$89.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Official Bay Harbor Red Wings game jersey featuring authentic team colors and logo. Made with moisture-wicking fabric for optimal performance on the ice."
  },

  {
    name: "Bay Harbor Red Wings Practice Hoodie",
    price: "$40.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Comfortable practice hoodie perfect for training sessions and casual wear. Features the Bay Harbor Red Wings logo and provides warmth during off-ice activities."
  },
  {
    name: "Bay Harbor Red Wings Water Bottle",
    price: "$7.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Stay hydrated with this durable Bay Harbor Red Wings water bottle. Features team colors and logo, perfect for practices, games, and everyday use."
  },
  {
    name: "Bay Harbor Red Wings Backpack",
    price: "$32.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Spacious team backpack with multiple compartments for hockey gear and school supplies. Features the Bay Harbor Red Wings logo and durable construction."
  },
  {
    name: "Bay Harbor Red Wings Pom Beanie",
    price: "$20.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Warm knit beanie with team colors and pom-pom detail. Perfect for cold rinks and showing your Bay Harbor Red Wings spirit during winter months."
  },
  {
    name: "Bay Harbor Red Wings Quarter Zip",
    price: "$48.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Versatile quarter-zip pullover featuring the Bay Harbor Red Wings logo. Perfect for layering and provides comfort during training or casual wear."
  },
  {
    name: "Bay Harbor Red Wings Hockey Pant Shells",
    price: "$44.99 ",
    image: "/bay-harbor_copy_1_.png",
    description: "Team-colored hockey pant shells designed to fit over protective padding. Features the Bay Harbor Red Wings colors and logo for a professional team appearance."
  },
  {
    name: "Bay Harbor Red Wings Long Sleeve Tee",
    price: "$28.99",
    image: "/bay-harbor_copy_1_.png",
    description: "Comfortable long sleeve t-shirt featuring the Bay Harbor Red Wings logo. Made from soft cotton blend, perfect for everyday wear and showing team pride."
  },

];

const TeamSales: React.FC = () => {
  // Memoize products to prevent unnecessary re-renders
  const memoizedProducts = useMemo(() => products, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <SEOHead 
        title="Team Sales - Bay Harbor Red Wings Hockey Equipment | Harbor City, Los Angeles"
        description="Official Bay Harbor Red Wings team merchandise and hockey equipment serving Harbor City, Los Angeles, and Southern California. Custom team gear, jerseys, gloves, and accessories available at The Ice Box."
        keywords={[
          'Bay Harbor Red Wings',
          'team sales Harbor City',
          'team sales Los Angeles',
          'hockey team gear Southern California',
          'custom hockey equipment Los Angeles',
          'team merchandise Harbor City',
          'hockey jerseys Los Angeles',
          'team gloves Southern California',
          'hockey team sales So Cal',
          'custom team gear Los Angeles',
          'hockey team equipment Harbor City',
          'team hockey gear Torrance',
          'hockey team sales San Pedro',
          'team merchandise Carson'
        ]}
        type="website"
        url="https://iceboxhockey.com/teamsales"
        image="/bay-harbor_copy_1_.png"
      />
      <main>
        <div className="grid gap-6" style={{gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))'}}>
          {memoizedProducts.map((product, idx) => (
            <div
              key={`${product.name}-${idx}`}
              className="bg-white rounded-lg shadow p-4 flex flex-col items-center hover:shadow-lg transition-shadow border border-gray-100"
            >
              <OptimizedImage
                src={product.image}
                alt={product.name}
                className="w-32 h-32 object-contain mb-4"
                width={128}
                height={128}
                loading="lazy"
              />
              <div className="font-semibold text-lg text-gray-900 mb-1 text-center">{product.name}</div>
              <div className="text-blue-700 font-bold text-base mb-2">{product.price}</div>
              {product.description && <p className="text-xs text-gray-500 mt-2 text-center">{product.description}</p>}
            </div>
          ))}
        </div>
      </main>
    </div>
  );
}

export default TeamSales;
