
import { Suspense, lazy } from 'react';
import Hero from '@/components/Hero';
import SEOHead from '@/components/SEOHead';

// Lazy load non-critical sections for better initial load performance
const About = lazy(() => import('@/components/About'));
const Services = lazy(() => import('@/components/Services'));
const Products = lazy(() => import('@/components/Products'));
const Brands = lazy(() => import('@/components/Brands'));

// Section loading component
const SectionLoader = () => (
  <div className="py-20 flex items-center justify-center" style={{ backgroundColor: '#1b263b' }}>
    <div className="animate-pulse flex space-x-4">
      <div className="rounded-full bg-blue-400/20 h-3 w-3"></div>
      <div className="rounded-full bg-blue-400/40 h-3 w-3"></div>
      <div className="rounded-full bg-blue-400/60 h-3 w-3"></div>
    </div>
  </div>
);

const Index = () => {
  return (
    <div className="min-h-screen">
      <SEOHead 
        title="The Ice Box - Premier Hockey Equipment Store | Harbor City, Los Angeles, CA"
        description="Professional hockey equipment store serving Harbor City, Los Angeles, and Southern California. Expert fitting services, premium gear from top brands including Bauer, CCM, and Warrior. Located inside Skating Edge Arena. Opening August 2025."
        keywords={[
          'hockey equipment Harbor City',
          'hockey equipment Los Angeles', 
          'hockey equipment Southern California',
          'hockey equipment So Cal',
          'ice hockey gear Harbor City',
          'hockey store Los Angeles',
          'hockey shop Southern California',
          'Skating Edge Arena',
          'hockey sticks Los Angeles',
          'hockey skates Harbor City',
          'protective gear Southern California',
          'team sales Los Angeles',
          'hockey store near me',
          'Torrance hockey equipment',
          'San Pedro hockey gear',
          'Carson hockey store',
          'Long Beach hockey equipment'
        ]}
        type="website"
        url="https://iceboxhockey.com"
        image="/og-image.jpg"
      />
      <Hero />
      <Suspense fallback={<SectionLoader />}>
        <About />
      </Suspense>
      <Suspense fallback={<SectionLoader />}>
        <Services />
      </Suspense>
      <Suspense fallback={<SectionLoader />}>
        <Products />
      </Suspense>
      <Suspense fallback={<SectionLoader />}>
        <Brands />
      </Suspense>
    </div>
  );
};

export default Index;
