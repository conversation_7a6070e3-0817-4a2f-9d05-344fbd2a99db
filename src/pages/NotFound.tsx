import React from "react";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import Footer from "@/components/Footer";
import SEOHead from "@/components/SEOHead";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <>
      <SEOHead 
        title="Page Not Found - 404 | The Ice Box Hockey"
        description="The page you're looking for doesn't exist. Return to The Ice Box Hockey homepage to find hockey equipment and gear."
        keywords={['404', 'page not found', 'hockey equipment', 'The Ice Box']}
        type="website"
      />
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">404</h1>
          <p className="text-xl text-gray-600 mb-4">Oops! Page not found</p>
          <a href="/" className="text-blue-500 hover:text-blue-700 underline">
            Return to Home
          </a>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default NotFound;
