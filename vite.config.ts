import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
    server: {
      host: "::",
      port: 8080,
    },
    plugins: [
      react({
        // Enable React Fast Refresh
        fastRefresh: true,
      }),
    ],
    resolve: {
      alias: {
        "@": new URL('./src', import.meta.url).pathname,
      },
    },
    build: {
      // Optimize build performance
      target: 'esnext',
      minify: 'esbuild',
      sourcemap: false,
      rollupOptions: {
        output: {
          // Manual chunk splitting for better caching
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            ui: ['@radix-ui/react-accordion', '@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
            utils: ['clsx', 'tailwind-merge', 'class-variance-authority'],
            icons: ['lucide-react'],
          },
        },
      },
      // Increase chunk size warning limit
      chunkSizeWarningLimit: 1000,
    },
    optimizeDeps: {
      // Pre-bundle dependencies for faster dev server startup
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'lucide-react',
        '@tanstack/react-query',
      ],
    },
    define: {
      'import.meta.env': {
        ...env,
        // Expose Google Maps API key
        VITE_GOOGLE_MAPS_API_KEY: JSON.stringify(env.VITE_GOOGLE_MAPS_API_KEY || ''),
        // Expose Google Analytics configuration
        VITE_GA_MEASUREMENT_ID: JSON.stringify(env.VITE_GA_MEASUREMENT_ID || 'G-VL34HQ8M9H'),
        VITE_GA_DEBUG: JSON.stringify(env.VITE_GA_DEBUG || 'false')
      }
    }
  };
});
