{"buildSettings": {"mode": "production", "sourceMaps": false, "minification": true}, "performanceThresholds": {"lcp": 2500, "fid": 100, "cls": 0.1, "lighthousePerformance": 90}, "accessibilityLevel": "AA", "testSuites": [{"name": "unit-tests", "type": "unit", "enabled": true, "timeout": 30000, "retries": 2}, {"name": "integration-tests", "type": "integration", "enabled": true, "timeout": 60000, "retries": 2}, {"name": "e2e-tests", "type": "e2e", "enabled": true, "timeout": 120000, "retries": 3}, {"name": "performance-tests", "type": "performance", "enabled": true, "timeout": 180000, "retries": 2}, {"name": "accessibility-tests", "type": "accessibility", "enabled": true, "timeout": 90000, "retries": 2}], "externalDependencies": [{"name": "Google Maps API", "url": "https://maps.googleapis.com/maps/api/js", "timeout": 10000, "critical": true}, {"name": "Google Fonts", "url": "https://fonts.googleapis.com", "timeout": 5000, "critical": false}]}