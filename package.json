{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "verify": "tsx scripts/verify-deployment.ts", "verify:verbose": "tsx scripts/verify-deployment.ts --verbose", "verify:json": "tsx scripts/verify-deployment.ts --output json", "demo:test-orchestrator": "tsx scripts/demo-test-orchestrator.ts", "demo:accessibility": "tsx scripts/demo-accessibility.ts", "demo:build-verification": "tsx scripts/build-verification-demo.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.5"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@babel/core": "^7.24.5", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.24.1", "@babel/preset-typescript": "^7.24.1", "@eslint/js": "^9.9.0", "@playwright/test": "^1.53.1", "@stagewise-plugins/react": "^0.4.8", "@stagewise/toolbar": "^0.4.8", "@stagewise/toolbar-react": "^0.4.8", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/axe-core": "^2.0.2", "@types/google.maps": "^3.58.1", "@types/jest": "^29.5.12", "@types/node": "^22.15.32", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.20", "axe-core": "^4.10.3", "babel-jest": "^29.7.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lighthouse": "^12.8.0", "playwright-lighthouse": "^4.0.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "tsx": "^4.20.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}