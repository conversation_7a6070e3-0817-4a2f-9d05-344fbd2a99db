import { test, expect } from '@playwright/test';

test.describe('Forms and Interactions', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:8080');
  });

  test('should handle contact form interactions', async ({ page }) => {
    // Look for contact form or contact section
    const contactSection = page.locator('[data-testid="contact"], #contact, .contact');
    
    if (await contactSection.isVisible()) {
      await contactSection.scrollIntoViewIfNeeded();
      
      // Check for form fields
      const nameField = page.locator('input[name="name"], input[placeholder*="name" i]');
      const emailField = page.locator('input[name="email"], input[type="email"]');
      const messageField = page.locator('textarea[name="message"], textarea[placeholder*="message" i]');
      
      if (await nameField.isVisible()) {
        await nameField.fill('Test User');
        expect(await nameField.inputValue()).toBe('Test User');
      }
      
      if (await emailField.isVisible()) {
        await emailField.fill('<EMAIL>');
        expect(await emailField.inputValue()).toBe('<EMAIL>');
      }
      
      if (await messageField.isVisible()) {
        await messageField.fill('This is a test message');
        expect(await messageField.inputValue()).toBe('This is a test message');
      }
    }
  });

  test('should validate email format in forms', async ({ page }) => {
    const emailField = page.locator('input[type="email"]').first();
    
    if (await emailField.isVisible()) {
      await emailField.fill('invalid-email');
      
      // Try to submit or trigger validation
      const submitButton = page.locator('button[type="submit"], input[type="submit"]').first();
      if (await submitButton.isVisible()) {
        await submitButton.click();
        
        // Check for validation message
        const validationMessage = await emailField.evaluate((el: HTMLInputElement) => el.validationMessage);
        expect(validationMessage).toBeTruthy();
      }
    }
  });

  test('should handle search functionality if present', async ({ page }) => {
    const searchInput = page.locator('input[type="search"], input[placeholder*="search" i]');
    
    if (await searchInput.isVisible()) {
      await searchInput.fill('hockey stick');
      
      // Look for search button or press Enter
      const searchButton = page.locator('button[type="submit"]').first();
      if (await searchButton.isVisible()) {
        await searchButton.click();
      } else {
        await searchInput.press('Enter');
      }
      
      // Wait for potential results or navigation
      await page.waitForTimeout(1000);
    }
  });

  test('should handle newsletter signup if present', async ({ page }) => {
    const newsletterInput = page.locator('input[placeholder*="newsletter" i], input[placeholder*="email" i]').last();
    
    if (await newsletterInput.isVisible()) {
      await newsletterInput.scrollIntoViewIfNeeded();
      await newsletterInput.fill('<EMAIL>');
      
      const subscribeButton = page.locator('button:has-text("Subscribe"), button:has-text("Sign up")').first();
      if (await subscribeButton.isVisible()) {
        await subscribeButton.click();
        
        // Check for success message or confirmation
        await page.waitForTimeout(1000);
      }
    }
  });

  test('should handle form field focus and blur events', async ({ page }) => {
    const formFields = page.locator('input, textarea, select');
    const fieldCount = await formFields.count();
    
    if (fieldCount > 0) {
      const firstField = formFields.first();
      
      // Test focus
      await firstField.focus();
      expect(await firstField.evaluate(el => document.activeElement === el)).toBe(true);
      
      // Test blur
      await page.keyboard.press('Tab');
      expect(await firstField.evaluate(el => document.activeElement === el)).toBe(false);
    }
  });

  test('should handle form submission prevention for incomplete forms', async ({ page }) => {
    const forms = page.locator('form');
    const formCount = await forms.count();
    
    if (formCount > 0) {
      const form = forms.first();
      const submitButton = form.locator('button[type="submit"], input[type="submit"]');
      
      if (await submitButton.isVisible()) {
        // Try to submit empty form
        await submitButton.click();
        
        // Check that we're still on the same page (form didn't submit)
        expect(page.url()).toContain('localhost:8081');
      }
    }
  });
});