/**
 * Automated performance tests for critical pages
 * Tests Core Web Vitals and Lighthouse scores
 */

import { test, expect, Browser } from '@playwright/test';
import { createPerformanceMonitor, PerformanceTestResult } from '../src/verification/performance';
import { createConfigManager } from '../src/verification/config';

// Critical pages to test
const CRITICAL_PAGES = [
  '/',           // Homepage
  '/team-sales', // Team Sales page
  '/harbor-city' // Harbor City page
];

// Performance test configuration
const PERFORMANCE_CONFIG = {
  timeout: 180000, // 3 minutes per test
  retries: 2,
};

test.describe('Performance Tests', () => {
  let performanceMonitor: ReturnType<typeof createPerformanceMonitor>;

  test.beforeAll(async () => {
    const configManager = createConfigManager();
    performanceMonitor = createPerformanceMonitor(configManager.getConfig());
  });

  test.describe('Core Web Vitals', () => {
    CRITICAL_PAGES.forEach((pagePath) => {
      test(`should meet Core Web Vitals thresholds for ${pagePath}`, async ({ page }) => {
        test.setTimeout(PERFORMANCE_CONFIG.timeout);

        const url = `http://localhost:8080${pagePath}`;
        const result = await performanceMonitor.runPerformanceTest(page, url);

        // Log performance metrics for debugging
        console.log(`Performance metrics for ${pagePath}:`, {
          LCP: `${result.metrics.lcp}ms`,
          FID: `${result.metrics.fid}ms`,
          CLS: result.metrics.cls,
          FCP: `${result.metrics.fcp}ms`,
          Lighthouse: result.metrics.lighthouse.performance,
        });

        // Assert Core Web Vitals thresholds
        expect(result.metrics.lcp, `LCP should be under 2500ms for ${pagePath}`).toBeLessThanOrEqual(2500);
        expect(result.metrics.fid, `FID should be under 100ms for ${pagePath}`).toBeLessThanOrEqual(100);
        expect(result.metrics.cls, `CLS should be under 0.1 for ${pagePath}`).toBeLessThanOrEqual(0.1);

        // Assert overall success
        expect(result.success, `Performance test should pass for ${pagePath}`).toBe(true);

        // Log any violations
        if (result.violations.length > 0) {
          console.warn(`Performance violations for ${pagePath}:`, result.violations);
        }
      });
    });
  });

  test.describe('Lighthouse Performance', () => {
    CRITICAL_PAGES.forEach((pagePath) => {
      test(`should achieve Lighthouse performance score >= 90 for ${pagePath}`, async ({ page }) => {
        test.setTimeout(PERFORMANCE_CONFIG.timeout);

        const url = `http://localhost:8080${pagePath}`;
        const result = await performanceMonitor.runPerformanceTest(page, url);

        // Log Lighthouse scores
        console.log(`Lighthouse scores for ${pagePath}:`, result.metrics.lighthouse);

        // Assert Lighthouse performance threshold
        expect(
          result.metrics.lighthouse.performance,
          `Lighthouse performance score should be >= 90 for ${pagePath}`
        ).toBeGreaterThanOrEqual(90);

        // Additional Lighthouse score checks (informational)
        expect(result.metrics.lighthouse.accessibility).toBeGreaterThanOrEqual(80);
        expect(result.metrics.lighthouse.bestPractices).toBeGreaterThanOrEqual(80);
        expect(result.metrics.lighthouse.seo).toBeGreaterThanOrEqual(80);
      });
    });
  });

  test.describe('Performance Regression', () => {
    test('should not have performance regressions across all critical pages', async ({ browser }) => {
      test.setTimeout(PERFORMANCE_CONFIG.timeout * CRITICAL_PAGES.length);

      const results = await performanceMonitor.testCriticalPages(browser, CRITICAL_PAGES);
      const report = performanceMonitor.generatePerformanceReport(results);

      // Log overall performance report
      console.log('Overall Performance Report:', {
        overallSuccess: report.overallSuccess,
        totalViolations: report.totalViolations,
        averageMetrics: report.averageMetrics,
        pageCount: results.length,
      });

      // Assert overall performance
      expect(report.overallSuccess, 'All pages should meet performance thresholds').toBe(true);
      expect(report.totalViolations, 'Should have no critical performance violations').toBe(0);

      // Assert average metrics meet thresholds
      expect(report.averageMetrics.lcp, 'Average LCP should be under 2500ms').toBeLessThanOrEqual(2500);
      expect(report.averageMetrics.fid, 'Average FID should be under 100ms').toBeLessThanOrEqual(100);
      expect(report.averageMetrics.cls, 'Average CLS should be under 0.1').toBeLessThanOrEqual(0.1);
      expect(
        report.averageMetrics.lighthouse.performance,
        'Average Lighthouse performance should be >= 90'
      ).toBeGreaterThanOrEqual(90);

      // Detailed assertions for each page
      results.forEach((result, index) => {
        const pagePath = CRITICAL_PAGES[index];
        expect(result.success, `${pagePath} should meet all performance thresholds`).toBe(true);
        
        // Log any failures for debugging
        if (!result.success) {
          console.error(`Performance failures for ${pagePath}:`, result.violations);
        }
      });
    });
  });

  test.describe('Performance Monitoring', () => {
    test('should collect comprehensive performance metrics', async ({ page }) => {
      const url = 'http://localhost:8080/';
      const result = await performanceMonitor.runPerformanceTest(page, url);

      // Verify all metrics are collected
      expect(typeof result.metrics.lcp).toBe('number');
      expect(typeof result.metrics.fid).toBe('number');
      expect(typeof result.metrics.cls).toBe('number');
      expect(typeof result.metrics.fcp).toBe('number');

      // Verify Lighthouse scores are collected
      expect(typeof result.metrics.lighthouse.performance).toBe('number');
      expect(typeof result.metrics.lighthouse.accessibility).toBe('number');
      expect(typeof result.metrics.lighthouse.bestPractices).toBe('number');
      expect(typeof result.metrics.lighthouse.seo).toBe('number');

      // Verify result structure
      expect(typeof result.success).toBe('boolean');
      expect(Array.isArray(result.violations)).toBe(true);
      expect(typeof result.duration).toBe('number');
      expect(typeof result.url).toBe('string');

      console.log('Complete performance metrics collected:', result.metrics);
    });

    test('should handle performance threshold violations correctly', async ({ page }) => {
      // Create a monitor with very strict thresholds to trigger violations
      const strictConfig = createConfigManager({
        performanceThresholds: {
          lcp: 100,  // Very strict - 100ms
          fid: 10,   // Very strict - 10ms
          cls: 0.01, // Very strict - 0.01
          lighthousePerformance: 99, // Very strict - 99/100
        },
      }).getConfig();

      const strictMonitor = createPerformanceMonitor(strictConfig);
      const url = 'http://localhost:8080/';
      const result = await strictMonitor.runPerformanceTest(page, url);

      // With strict thresholds, we expect violations
      expect(result.violations.length).toBeGreaterThan(0);
      expect(result.success).toBe(false);

      // Verify violation structure
      result.violations.forEach(violation => {
        expect(typeof violation.metric).toBe('string');
        expect(typeof violation.actual).toBe('number');
        expect(typeof violation.threshold).toBe('number');
        expect(['warning', 'error']).toContain(violation.severity);
      });

      console.log('Performance violations detected (expected with strict thresholds):', result.violations);
    });
  });
});

// Helper test for development/debugging
test.describe('Performance Development Tools', () => {
  test.skip('Performance debugging - run manually for detailed analysis', async ({ page }) => {
    const url = 'http://localhost:8080/';
    
    // Navigate and wait for full load
    await page.goto(url, { waitUntil: 'networkidle' });
    
    // Collect detailed performance timing
    const performanceTiming = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      const resources = performance.getEntriesByType('resource');
      
      return {
        navigation: {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalTime: navigation.loadEventEnd - navigation.fetchStart,
        },
        paint: paint.map(entry => ({
          name: entry.name,
          startTime: entry.startTime,
        })),
        resources: resources.length,
        memory: (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        } : null,
      };
    });

    console.log('Detailed Performance Analysis:', performanceTiming);
  });
});