import { test, expect } from '@playwright/test';

test.describe('Visual Regression Tests', () => {
  test('should match homepage screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Wait for images to load
    await page.waitForFunction(() => {
      const images = Array.from(document.querySelectorAll('img'));
      return images.every(img => img.complete);
    });
    
    // Take full page screenshot
    await expect(page).toHaveScreenshot('homepage-full.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match hero section screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    const heroSection = page.locator('.hero, [data-testid="hero"], section').first();
    await heroSection.waitFor({ state: 'visible' });
    
    await expect(heroSection).toHaveScreenshot('hero-section.png', {
      animations: 'disabled'
    });
  });

  test('should match navigation screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    const navigation = page.locator('nav, header nav, .navigation');
    if (await navigation.count() > 0) {
      await expect(navigation.first()).toHaveScreenshot('navigation.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match footer screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    const footer = page.locator('footer');
    if (await footer.count() > 0) {
      await footer.scrollIntoViewIfNeeded();
      await expect(footer).toHaveScreenshot('footer.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match brands section screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    const brandsSection = page.locator('[data-testid="brands"], .brands, section:has(img[alt*="brand" i])');
    if (await brandsSection.count() > 0) {
      await brandsSection.first().scrollIntoViewIfNeeded();
      
      // Wait for brand images to load
      await page.waitForFunction(() => {
        const brandImages = Array.from(document.querySelectorAll('img[alt*="brand" i], .brands img'));
        return brandImages.every((img: HTMLImageElement) => img.complete);
      });
      
      await expect(brandsSection.first()).toHaveScreenshot('brands-section.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match mobile navigation screenshot', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Look for mobile menu trigger
    const menuTrigger = page.locator('button:has-text("menu"), .menu-toggle, [aria-label*="menu" i]');
    if (await menuTrigger.count() > 0) {
      await menuTrigger.first().click();
      await page.waitForTimeout(500); // Wait for menu animation
      
      const mobileMenu = page.locator('.mobile-menu, .menu-open, nav[aria-expanded="true"]');
      if (await mobileMenu.count() > 0) {
        await expect(mobileMenu.first()).toHaveScreenshot('mobile-navigation.png', {
          animations: 'disabled'
        });
      }
    }
  });

  test('should match products section screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    const productsSection = page.locator('[data-testid="products"], .products, section:has-text("Products")');
    if (await productsSection.count() > 0) {
      await productsSection.first().scrollIntoViewIfNeeded();
      
      await expect(productsSection.first()).toHaveScreenshot('products-section.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match services section screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    const servicesSection = page.locator('[data-testid="services"], .services, section:has-text("Services")');
    if (await servicesSection.count() > 0) {
      await servicesSection.first().scrollIntoViewIfNeeded();
      
      await expect(servicesSection.first()).toHaveScreenshot('services-section.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match about section screenshot', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    const aboutSection = page.locator('[data-testid="about"], .about, section:has-text("About")');
    if (await aboutSection.count() > 0) {
      await aboutSection.first().scrollIntoViewIfNeeded();
      
      await expect(aboutSection.first()).toHaveScreenshot('about-section.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match tablet viewport screenshot', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Wait for images to load
    await page.waitForFunction(() => {
      const images = Array.from(document.querySelectorAll('img'));
      return images.every(img => img.complete);
    });
    
    await expect(page).toHaveScreenshot('homepage-tablet.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match mobile viewport screenshot', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Wait for images to load
    await page.waitForFunction(() => {
      const images = Array.from(document.querySelectorAll('img'));
      return images.every(img => img.complete);
    });
    
    await expect(page).toHaveScreenshot('homepage-mobile.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match hover states for interactive elements', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Test button hover states
    const buttons = page.locator('button, .btn, a[role="button"]');
    const buttonCount = await buttons.count();
    
    if (buttonCount > 0) {
      const firstButton = buttons.first();
      await firstButton.scrollIntoViewIfNeeded();
      await firstButton.hover();
      
      await expect(firstButton).toHaveScreenshot('button-hover.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match focus states for accessibility', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Test focus states
    const focusableElements = page.locator('a, button, input, select, textarea');
    const elementCount = await focusableElements.count();
    
    if (elementCount > 0) {
      const firstElement = focusableElements.first();
      await firstElement.scrollIntoViewIfNeeded();
      await firstElement.focus();
      
      await expect(firstElement).toHaveScreenshot('element-focus.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match dark mode if supported', async ({ page }) => {
    await page.goto('http://localhost:8080');
    await page.waitForLoadState('networkidle');
    
    // Check if dark mode toggle exists
    const darkModeToggle = page.locator('[data-theme="dark"], .dark-mode-toggle, button:has-text("dark")');
    
    if (await darkModeToggle.count() > 0) {
      await darkModeToggle.first().click();
      await page.waitForTimeout(500); // Wait for theme transition
      
      await expect(page).toHaveScreenshot('homepage-dark-mode.png', {
        fullPage: true,
        animations: 'disabled'
      });
    }
  });
});