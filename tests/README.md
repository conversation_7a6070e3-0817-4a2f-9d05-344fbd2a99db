# Playwright Test Suite for Ice Box Hockey

This directory contains comprehensive end-to-end tests for the Ice Box Hockey website using <PERSON>wright. The test suite covers multiple aspects of the website to ensure quality, performance, and accessibility.

## Test Files Overview

### 1. `homepage.spec.ts`
**Purpose**: Tests the main homepage functionality and content

**Test Coverage**:
- Page loading and title verification
- Navigation menu visibility and functionality
- Hero section content and display
- Brand logos section
- Footer content and contact information
- Mobile responsiveness
- Product/services information display

### 2. `navigation.spec.ts`
**Purpose**: Tests website navigation and menu interactions

**Test Coverage**:
- Navigation menu visibility across different viewport sizes
- Mobile menu toggle functionality
- Navigation link functionality and section scrolling
- Accessibility attributes (ARIA labels, roles)
- Keyboard navigation support
- Navigation state persistence across interactions

### 3. `forms.spec.ts`
**Purpose**: Tests form interactions and user input handling

**Test Coverage**:
- Contact form field interactions
- Email format validation
- Search functionality (if present)
- Newsletter signup forms
- Form field focus and blur events
- Form submission prevention for incomplete forms

### 4. `accessibility.spec.ts`
**Purpose**: Ensures the website meets accessibility standards

**Test Coverage**:
- Proper heading hierarchy (h1-h6)
- Alt text for all images
- Form labels and accessible names
- Keyboard navigation support
- ARIA attributes for interactive elements
- Color contrast checks
- Skip links and landmark structure
- Focus management for modals/dialogs
- Page title and language attributes

### 5. `performance.spec.ts`
**Purpose**: Tests website performance and optimization

**Test Coverage**:
- Page load time measurements
- Image optimization checks
- Layout shift measurements (CLS)
- First Contentful Paint (FCP) timing
- Resource loading efficiency
- CSS and JavaScript file size validation
- Slow network condition handling
- Caching header verification
- Mobile viewport performance

### 6. `visual.spec.ts`
**Purpose**: Visual regression testing to catch unintended UI changes

**Test Coverage**:
- Full homepage screenshots
- Individual section screenshots (hero, navigation, footer, brands, products, services, about)
- Mobile and tablet viewport screenshots
- Interactive element hover states
- Focus states for accessibility
- Dark mode support (if available)

## Running the Tests

### Prerequisites
1. Ensure the development server is running:
   ```bash
   npm run dev
   ```
   The server should be accessible at `http://localhost:8080`

2. Install Playwright browsers (if not already done):
   ```bash
   npx playwright install
   ```

### Running All Tests
```bash
# Run all tests
npx playwright test

# Run with specific reporter
npx playwright test --reporter=line

# Run in headed mode (visible browser)
npx playwright test --headed
```

### Running Specific Test Files
```bash
# Run homepage tests only
npx playwright test tests/homepage.spec.ts

# Run accessibility tests only
npx playwright test tests/accessibility.spec.ts

# Run performance tests only
npx playwright test tests/performance.spec.ts
```

### Running Tests for Specific Browsers
```bash
# Run tests in Chromium only
npx playwright test --project=chromium

# Run tests in Firefox only
npx playwright test --project=firefox

# Run tests in WebKit only
npx playwright test --project=webkit
```

## Test Configuration

The tests are configured in `playwright.config.ts` with the following settings:
- **Base URL**: `http://localhost:8080`
- **Browsers**: Chromium, Firefox, WebKit
- **Parallel execution**: Enabled for faster test runs
- **Screenshots**: Captured on failure
- **Videos**: Recorded for failed tests
- **Traces**: Collected on retry

## Visual Testing

The visual regression tests create screenshot baselines on first run. These screenshots are stored in the `test-results` directory and should be committed to version control to track visual changes over time.

### Updating Visual Baselines
If intentional visual changes are made to the website:
```bash
# Update all visual baselines
npx playwright test tests/visual.spec.ts --update-snapshots

# Update specific test baselines
npx playwright test tests/visual.spec.ts:"should match homepage screenshot" --update-snapshots
```

## Test Reports

Playwright generates detailed HTML reports after test runs:
```bash
# Open the HTML report
npx playwright show-report
```

The report includes:
- Test results and timing
- Screenshots of failures
- Video recordings
- Trace files for debugging

## Best Practices

1. **Keep tests independent**: Each test should be able to run in isolation
2. **Use data-testid attributes**: For reliable element selection in the application
3. **Wait for elements**: Use `waitFor()` methods instead of fixed timeouts
4. **Test real user scenarios**: Focus on user workflows rather than implementation details
5. **Maintain visual baselines**: Regularly review and update screenshot baselines

## Troubleshooting

### Common Issues

1. **Connection refused errors**: Ensure the development server is running on port 8080
2. **Element not found**: Check if the website structure has changed
3. **Timeout errors**: Increase timeout values for slow-loading content
4. **Visual test failures**: Review screenshot differences and update baselines if changes are intentional

### Debug Mode
Run tests in debug mode for step-by-step execution:
```bash
npx playwright test --debug
```

### Trace Viewer
View detailed traces of test execution:
```bash
npx playwright show-trace test-results/[test-name]/trace.zip
```

## Contributing

When adding new tests:
1. Follow the existing naming conventions
2. Add appropriate test descriptions
3. Include both positive and negative test cases
4. Update this README with new test coverage
5. Ensure tests are cross-browser compatible

## Maintenance

Regularly:
1. Update Playwright to the latest version
2. Review and update test selectors if the UI changes
3. Monitor test execution times and optimize slow tests
4. Update visual baselines when UI changes are intentional
5. Review accessibility standards and update tests accordingly