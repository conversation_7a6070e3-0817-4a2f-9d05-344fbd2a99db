import { test, expect } from '@playwright/test';

/**
 * Test suite for Ice Box Hockey homepage functionality
 * This test explores the main navigation and hero section of the website
 */
test.describe('Ice Box Hockey Homepage', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('/');
  });

  test('should load homepage successfully', async ({ page }) => {
    // Verify the page loads and has the correct title
    await expect(page).toHaveTitle(/Ice Box Hockey|Vite/);
    
    // Check that the main content is visible
    await expect(page.locator('body')).toBeVisible();
  });

  test('should display main navigation menu', async ({ page }) => {
    // Look for navigation elements that are likely present
    // Based on the components structure, there should be a Menu component
    const navigation = page.getByRole('navigation').first();
    await expect(navigation).toBeVisible();
  });

  test('should display hero section with branding', async ({ page }) => {
    // Check for hero section content
    // The Hero component should contain the main branding
    const heroSection = page.locator('[data-testid="hero"]').or(
      page.locator('section').first()
    );
    await expect(heroSection).toBeVisible();
    
    // Look for the Ice Box logo or branding
    const logo = page.getByRole('img', { name: /ice.*box|logo/i }).first();
    await expect(logo).toBeVisible();
  });

  test('should display brand logos section', async ({ page }) => {
    // Based on the public folder, there are many brand images
    // The Brands component should display these
    const brandsSection = page.locator('[data-testid="brands"]').or(
      page.getByText(/brands|partners/i).locator('..').first()
    );
    
    // Check if brand images are loaded
    const brandImages = page.getByRole('img').filter({
      has: page.locator('[src*="Bauer"], [src*="CCM"], [src*="Warrior"]')
    });
    
    await expect(brandImages.first()).toBeVisible();
  });

  test('should have working footer with contact information', async ({ page }) => {
    // Scroll to footer to ensure it's in view
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Check footer is visible
    const footer = page.locator('footer');
    await expect(footer).toBeVisible();
    
    // Look for contact information or links
    const contactInfo = footer.getByText(/contact|phone|email|address/i).first();
    await expect(contactInfo).toBeVisible();
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Verify page still loads correctly
    await expect(page.locator('body')).toBeVisible();
    
    // Check if mobile menu toggle is present (common pattern)
    const mobileMenuToggle = page.getByRole('button', { name: /menu|toggle|hamburger/i }).first();
    
    // Mobile menu toggle should be visible on small screens
    await expect(mobileMenuToggle).toBeVisible();
  });

  test('should load and display product/services information', async ({ page }) => {
    // Based on components, there are Products and Services components
    const productsSection = page.locator('[data-testid="products"]').or(
      page.getByText(/products|equipment|gear/i).locator('..').first()
    );
    
    const servicesSection = page.locator('[data-testid="services"]').or(
      page.getByText(/services|repairs|maintenance/i).locator('..').first()
    );
    
    // At least one of these sections should be visible
    await expect(productsSection.or(servicesSection)).toBeVisible();
  });
});