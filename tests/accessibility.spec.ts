import { test, expect } from '@playwright/test';
import { AxeBuilder } from '@axe-core/playwright';

test.describe('Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:4175');
  });

  test('should have proper heading hierarchy', async ({ page }) => {
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    
    // Should have at least one h1
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBeGreaterThanOrEqual(1);
    
    // Check that headings have text content
    for (const heading of headings) {
      const text = await heading.textContent();
      expect(text?.trim()).toBeTruthy();
    }
  });

  test('should have alt text for all images', async ({ page }) => {
    const images = await page.locator('img').all();
    
    for (const img of images) {
      const alt = await img.getAttribute('alt');
      const src = await img.getAttribute('src');
      
      // Images should have alt attribute (can be empty for decorative images)
      expect(alt).not.toBeNull();
      
      // If image has meaningful src, alt should not be empty
      if (src && !src.includes('placeholder') && !src.includes('decoration')) {
        expect(alt?.trim()).toBeTruthy();
      }
    }
  });

  test('should have proper form labels', async ({ page }) => {
    const inputs = await page.locator('input[type="text"], input[type="email"], input[type="tel"], textarea').all();
    
    for (const input of inputs) {
      const id = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledBy = await input.getAttribute('aria-labelledby');
      const placeholder = await input.getAttribute('placeholder');
      
      // Input should have some form of labeling
      const hasLabel = id ? await page.locator(`label[for="${id}"]`).count() > 0 : false;
      const hasAccessibleName = hasLabel || ariaLabel || ariaLabelledBy || placeholder;
      
      expect(hasAccessibleName).toBe(true);
    }
  });

  test('should have keyboard navigation support', async ({ page }) => {
    // Test tab navigation through interactive elements
    const interactiveElements = page.locator('a, button, input, select, textarea, [tabindex]');
    const count = await interactiveElements.count();
    
    if (count > 0) {
      // Focus first element
      await page.keyboard.press('Tab');
      
      let focusedElement = await page.evaluate(() => document.activeElement?.tagName);
      expect(['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'].includes(focusedElement || '')).toBe(true);
      
      // Test a few more tab presses
      for (let i = 0; i < Math.min(3, count - 1); i++) {
        await page.keyboard.press('Tab');
        focusedElement = await page.evaluate(() => document.activeElement?.tagName);
        expect(focusedElement).toBeTruthy();
      }
    }
  });

  test('should have proper ARIA attributes for interactive elements', async ({ page }) => {
    // Check buttons have proper roles and states
    const buttons = await page.locator('button, [role="button"]').all();
    
    for (const button of buttons) {
      const ariaLabel = await button.getAttribute('aria-label');
      const textContent = await button.textContent();
      const ariaLabelledBy = await button.getAttribute('aria-labelledby');
      
      // Button should have accessible name
      const hasAccessibleName = ariaLabel || textContent?.trim() || ariaLabelledBy;
      expect(hasAccessibleName).toBeTruthy();
    }
  });

  test('should have proper color contrast (basic check)', async ({ page }) => {
    // Check that text elements have sufficient color contrast
    const textElements = await page.locator('p, h1, h2, h3, h4, h5, h6, span, div').all();
    
    for (const element of textElements.slice(0, 10)) { // Check first 10 elements
      const styles = await element.evaluate((el) => {
        const computed = window.getComputedStyle(el);
        return {
          color: computed.color,
          backgroundColor: computed.backgroundColor,
          fontSize: computed.fontSize
        };
      });
      
      // Basic check - ensure color is not the same as background
      expect(styles.color).not.toBe(styles.backgroundColor);
    }
  });

  test('should have skip links or proper navigation structure', async ({ page }) => {
    // Check for skip links
    const skipLinks = await page.locator('a[href^="#"]').all();
    const hasSkipToContent = skipLinks.some(async (link) => {
      const text = await link.textContent();
      return text?.toLowerCase().includes('skip') || text?.toLowerCase().includes('main');
    });
    
    // Check for proper landmark structure
    const landmarks = await page.locator('main, nav, header, footer, aside, section[aria-label]').count();
    
    // Should have either skip links or proper landmarks
    expect(hasSkipToContent || landmarks > 0).toBe(true);
  });

  test('should handle focus management for modals/dialogs', async ({ page }) => {
    // Look for modal triggers
    const modalTriggers = page.locator('[data-modal], [aria-haspopup="dialog"], button:has-text("menu"), button:has-text("open")');
    const triggerCount = await modalTriggers.count();
    
    if (triggerCount > 0) {
      const firstTrigger = modalTriggers.first();
      await firstTrigger.click();
      
      // Wait for potential modal to appear
      await page.waitForTimeout(500);
      
      // Check if a modal/dialog appeared
      const modal = page.locator('[role="dialog"], .modal, .popup');
      if (await modal.isVisible()) {
        // Focus should be trapped in modal
        const focusedElement = await page.evaluate(() => document.activeElement);
        expect(focusedElement).toBeTruthy();
        
        // Test Escape key to close modal
        await page.keyboard.press('Escape');
        await page.waitForTimeout(300);
        
        // Modal should be closed or focus returned
        const isModalStillVisible = await modal.isVisible();
        if (isModalStillVisible) {
          // If modal is still visible, it might not support Escape key
          // This is not necessarily a failure, just note it
        }
      }
    }
  });

  test('should have proper page title', async ({ page }) => {
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(0);
    expect(title).not.toBe('Document'); // Should not be default title
  });

  test('should have lang attribute on html element', async ({ page }) => {
    const lang = await page.locator('html').getAttribute('lang');
    expect(lang).toBeTruthy();
    expect(lang).toMatch(/^[a-z]{2}(-[A-Z]{2})?$/); // Should be valid language code
  });

  test('should pass axe-core WCAG 2.1 AA compliance check', async ({ page }) => {
    // Run accessibility check using AxeBuilder
    const axeBuilder = new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa'])
      .withRules([
        'color-contrast',
        'aria-valid-attr',
        'aria-valid-attr-value',
        'button-name',
        'form-field-multiple-labels',
        'html-has-lang',
        'html-lang-valid',
        'image-alt',
        'input-image-alt',
        'label',
        'link-name',
        'page-has-heading-one',
        'region',
      ]);

    const results = await axeBuilder.analyze();
    
    // Check for violations
    if (results.violations.length > 0) {
      // Log violations for debugging
      console.log('Accessibility violations found:');
      results.violations.forEach((violation, index) => {
        console.log(`${index + 1}. ${violation.id}: ${violation.description}`);
        console.log(`   Impact: ${violation.impact}`);
        console.log(`   Help: ${violation.help}`);
        console.log(`   Elements: ${violation.nodes.map(n => n.target.join(', ')).join('; ')}`);
        console.log(`   More info: ${violation.helpUrl}\n`);
      });

      // Fail the test with detailed information
      throw new Error(`Found ${results.violations.length} accessibility violations. See console for details.`);
    }

    // Log success
    console.log(`✓ Accessibility check passed with ${results.passes.length} successful checks`);
  });

  test('should pass axe-core check on team sales page', async ({ page }) => {
    await page.goto('http://localhost:4175/team-sales');
    
    const axeBuilder = new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa']);

    const results = await axeBuilder.analyze();
    
    if (results.violations.length > 0) {
      console.log(`Team Sales page violations: ${results.violations.length}`);
      results.violations.forEach(v => console.log(`- ${v.id}: ${v.description}`));
      throw new Error(`Found ${results.violations.length} accessibility violations on team sales page`);
    }
    
    console.log(`✓ Team Sales page accessibility check passed`);
  });

  test('should pass axe-core check on harbor city page', async ({ page }) => {
    await page.goto('http://localhost:4175/harbor-city');
    
    const axeBuilder = new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa']);

    const results = await axeBuilder.analyze();
    
    if (results.violations.length > 0) {
      console.log(`Harbor City page violations: ${results.violations.length}`);
      results.violations.forEach(v => console.log(`- ${v.id}: ${v.description}`));
      throw new Error(`Found ${results.violations.length} accessibility violations on harbor city page`);
    }
    
    console.log(`✓ Harbor City page accessibility check passed`);
  });

  test('should have proper keyboard navigation flow', async ({ page }) => {
    // Test comprehensive keyboard navigation
    const interactiveElements = page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    const count = await interactiveElements.count();
    
    if (count === 0) {
      console.warn('No focusable elements found for keyboard navigation test');
      return;
    }

    // Start keyboard navigation
    await page.keyboard.press('Tab');
    
    const focusedElements: string[] = [];
    
    // Navigate through first 10 elements or all if fewer
    for (let i = 0; i < Math.min(10, count); i++) {
      const activeElement = await page.evaluate(() => {
        const active = document.activeElement;
        return {
          tagName: active?.tagName,
          id: active?.id,
          className: active?.className,
          textContent: active?.textContent?.trim().substring(0, 30),
          ariaLabel: active?.getAttribute('aria-label'),
        };
      });

      // Verify element is focusable and visible
      expect(activeElement.tagName).toBeTruthy();
      
      // Check if element is visible
      const isVisible = await page.evaluate(() => {
        const active = document.activeElement as HTMLElement;
        if (!active) return false;
        
        const rect = active.getBoundingClientRect();
        const style = window.getComputedStyle(active);
        
        return rect.width > 0 && 
               rect.height > 0 && 
               style.visibility !== 'hidden' && 
               style.display !== 'none' &&
               style.opacity !== '0';
      });

      expect(isVisible).toBe(true);
      
      focusedElements.push(`${activeElement.tagName}${activeElement.id ? `#${activeElement.id}` : ''}`);
      
      if (i < Math.min(9, count - 1)) {
        await page.keyboard.press('Tab');
      }
    }

    // Test reverse navigation
    await page.keyboard.press('Shift+Tab');
    const reverseFocused = await page.evaluate(() => document.activeElement?.tagName);
    expect(reverseFocused).toBeTruthy();

    console.log(`Keyboard navigation tested through ${focusedElements.length} elements:`, focusedElements);
  });

  test('should handle Enter and Space key activation', async ({ page }) => {
    // Find buttons and button-like elements
    const buttons = page.locator('button, [role="button"], input[type="button"], input[type="submit"]');
    const buttonCount = await buttons.count();

    if (buttonCount === 0) {
      console.warn('No buttons found for keyboard activation test');
      return;
    }

    // Test first few buttons
    for (let i = 0; i < Math.min(3, buttonCount); i++) {
      const button = buttons.nth(i);
      
      // Focus the button
      await button.focus();
      
      // Verify it's focused
      const isFocused = await button.evaluate(el => el === document.activeElement);
      expect(isFocused).toBe(true);

      // Check if button has accessible name
      const accessibleName = await button.evaluate(el => {
        return el.getAttribute('aria-label') || 
               el.textContent?.trim() || 
               el.getAttribute('title') ||
               el.getAttribute('aria-labelledby');
      });
      
      expect(accessibleName).toBeTruthy();

      // Note: We don't actually press Enter/Space to avoid side effects
      // but we verify the button is properly focusable and labeled
    }
  });

  test('should have proper ARIA landmarks and structure', async ({ page }) => {
    // Check for main landmark
    const mainLandmark = page.locator('main, [role="main"]');
    const mainCount = await mainLandmark.count();
    expect(mainCount).toBeGreaterThanOrEqual(1);

    // Check for navigation landmark
    const navLandmark = page.locator('nav, [role="navigation"]');
    const navCount = await navLandmark.count();
    expect(navCount).toBeGreaterThanOrEqual(1);

    // Check for banner (header) landmark
    const bannerLandmark = page.locator('header, [role="banner"]');
    const bannerCount = await bannerLandmark.count();
    expect(bannerCount).toBeGreaterThanOrEqual(1);

    // Check for contentinfo (footer) landmark
    const contentinfoLandmark = page.locator('footer, [role="contentinfo"]');
    const contentinfoCount = await contentinfoLandmark.count();
    expect(contentinfoCount).toBeGreaterThanOrEqual(1);

    // Verify heading hierarchy
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
    const headingLevels: number[] = [];

    for (const heading of headings) {
      const tagName = await heading.evaluate(el => el.tagName);
      const level = parseInt(tagName.charAt(1));
      headingLevels.push(level);
    }

    // Should start with h1
    if (headingLevels.length > 0) {
      expect(headingLevels[0]).toBe(1);

      // Check for proper hierarchy (no skipping levels)
      for (let i = 1; i < headingLevels.length; i++) {
        const currentLevel = headingLevels[i];
        const previousLevel = headingLevels[i - 1];
        
        // Current level should not be more than 1 level deeper than previous
        if (currentLevel > previousLevel) {
          expect(currentLevel - previousLevel).toBeLessThanOrEqual(1);
        }
      }
    }
  });

  test('should have proper form accessibility', async ({ page }) => {
    const formInputs = page.locator('input[type="text"], input[type="email"], input[type="tel"], input[type="password"], textarea, select');
    const inputCount = await formInputs.count();

    if (inputCount === 0) {
      console.warn('No form inputs found for form accessibility test');
      return;
    }

    for (let i = 0; i < inputCount; i++) {
      const input = formInputs.nth(i);
      const inputId = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledBy = await input.getAttribute('aria-labelledby');
      const ariaDescribedBy = await input.getAttribute('aria-describedby');

      // Check for associated label
      let hasLabel = false;
      if (inputId) {
        const labelCount = await page.locator(`label[for="${inputId}"]`).count();
        hasLabel = labelCount > 0;
      }

      // Input should have some form of accessible name
      const hasAccessibleName = hasLabel || ariaLabel || ariaLabelledBy;
      expect(hasAccessibleName).toBe(true);

      // Check for required field indication
      const isRequired = await input.getAttribute('required');
      const ariaRequired = await input.getAttribute('aria-required');
      
      if (isRequired !== null || ariaRequired === 'true') {
        // Required fields should be properly indicated
        // This is more of a warning than a hard requirement
        console.log(`Required field found: ${inputId || 'unnamed input'}`);
      }

      // Check for error states
      const ariaInvalid = await input.getAttribute('aria-invalid');
      if (ariaInvalid === 'true') {
        // Invalid fields should have error descriptions
        expect(ariaDescribedBy).toBeTruthy();
      }
    }
  });

  test('should handle focus management for interactive components', async ({ page }) => {
    // Test modal/dialog focus management if any exist
    const modalTriggers = page.locator('[data-modal], [aria-haspopup="dialog"], button:has-text("menu"), button:has-text("Menu")');
    const triggerCount = await modalTriggers.count();

    if (triggerCount > 0) {
      const trigger = modalTriggers.first();
      
      // Focus and activate the trigger
      await trigger.focus();
      await trigger.press('Enter');
      
      // Wait for modal to appear
      await page.waitForTimeout(500);
      
      // Check if modal appeared and has proper focus management
      const modal = page.locator('[role="dialog"], .modal, [aria-modal="true"]');
      const isModalVisible = await modal.isVisible();
      
      if (isModalVisible) {
        // Focus should be trapped in modal
        const focusedElement = await page.evaluate(() => document.activeElement?.tagName);
        expect(focusedElement).toBeTruthy();
        
        // Modal should have proper ARIA attributes
        const ariaModal = await modal.getAttribute('aria-modal');
        const role = await modal.getAttribute('role');
        
        expect(ariaModal === 'true' || role === 'dialog').toBe(true);
        
        // Test Escape key
        await page.keyboard.press('Escape');
        await page.waitForTimeout(300);
        
        // Check if modal closed
        const isStillVisible = await modal.isVisible();
        if (isStillVisible) {
          console.warn('Modal did not close with Escape key');
        }
      }
    }
  });
});