import { test, expect } from '@playwright/test';

/**
 * Test suite for Ice Box Hockey navigation functionality
 * This test focuses on menu interactions and navigation behavior
 */
test.describe('Ice Box Hockey Navigation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('/');
  });

  test('should display and interact with main navigation menu', async ({ page }) => {
    // Look for the main navigation menu
    const navigation = page.getByRole('navigation').first();
    await expect(navigation).toBeVisible();

    // Check for common navigation links
    const homeLink = page.getByRole('link', { name: /home/<USER>
    const aboutLink = page.getByRole('link', { name: /about/i }).first();
    const servicesLink = page.getByRole('link', { name: /services/i }).first();
    const productsLink = page.getByRole('link', { name: /products/i }).first();
    const contactLink = page.getByRole('link', { name: /contact/i }).first();

    // At least one navigation link should be present
    const anyNavLink = homeLink.or(aboutLink).or(servicesLink).or(productsLink).or(contactLink);
    await expect(anyNavLink).toBeVisible();
  });

  test('should handle mobile menu toggle functionality', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Look for mobile menu toggle button
    const mobileMenuToggle = page.getByRole('button', { name: /menu|toggle|hamburger/i }).first();
    
    if (await mobileMenuToggle.isVisible()) {
      // Click the mobile menu toggle
      await mobileMenuToggle.click();
      
      // Wait for menu to appear (could be animated)
      await page.waitForTimeout(500);
      
      // Check if navigation menu becomes visible after toggle
      const mobileNav = page.getByRole('navigation').first();
      await expect(mobileNav).toBeVisible();
      
      // Click toggle again to close menu
      await mobileMenuToggle.click();
      await page.waitForTimeout(500);
    }
  });

  test('should navigate to different sections when clicking nav links', async ({ page }) => {
    // Look for internal navigation links (not external)
    const navLinks = page.getByRole('navigation').getByRole('link');
    const linkCount = await navLinks.count();
    
    if (linkCount > 0) {
      // Get the first internal link
      const firstLink = navLinks.first();
      const linkText = await firstLink.textContent();
      
      // Click the link
      await firstLink.click();
      
      // Wait for navigation to complete
      await page.waitForLoadState('networkidle');
      
      // Verify we're still on the same domain (internal navigation)
      const currentUrl = page.url();
      expect(currentUrl).toContain('localhost:8081');
    }
  });

  test('should have accessible navigation with proper ARIA labels', async ({ page }) => {
    // Check for navigation with proper accessibility attributes
    const navigation = page.getByRole('navigation').first();
    
    if (await navigation.isVisible()) {
      // Navigation should have proper role
      await expect(navigation).toHaveAttribute('role', 'navigation');
      
      // Check for navigation links with proper accessibility
      const navLinks = navigation.getByRole('link');
      const linkCount = await navLinks.count();
      
      if (linkCount > 0) {
        // First link should be accessible
        const firstLink = navLinks.first();
        await expect(firstLink).toBeVisible();
        
        // Link should have text content or aria-label
        const hasText = await firstLink.textContent();
        const hasAriaLabel = await firstLink.getAttribute('aria-label');
        
        expect(hasText || hasAriaLabel).toBeTruthy();
      }
    }
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Focus on the first navigation link
    const firstNavLink = page.getByRole('navigation').getByRole('link').first();
    
    if (await firstNavLink.isVisible()) {
      await firstNavLink.focus();
      
      // Verify the link is focused
      await expect(firstNavLink).toBeFocused();
      
      // Test Tab navigation
      await page.keyboard.press('Tab');
      
      // Should move focus to next focusable element
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
    }
  });

  test('should maintain navigation state across page interactions', async ({ page }) => {
    // Get initial navigation state
    const navigation = page.getByRole('navigation').first();
    await expect(navigation).toBeVisible();
    
    // Scroll down the page
    await page.evaluate(() => window.scrollTo(0, 500));
    
    // Navigation should still be accessible
    await expect(navigation).toBeVisible();
    
    // Scroll back to top
    await page.evaluate(() => window.scrollTo(0, 0));
    
    // Navigation should still be visible
    await expect(navigation).toBeVisible();
  });
});